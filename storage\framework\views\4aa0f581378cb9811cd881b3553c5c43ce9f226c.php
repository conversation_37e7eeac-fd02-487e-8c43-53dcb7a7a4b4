
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="shortcut icon" href="<?php echo e(getSettingImage('app_fav_icon')); ?>" type="image/x-icon">
        <title>Update Your Application </title>
        <link href="https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="<?php echo e(asset('zaifiles/assets/css/bootstrap.min.css')); ?>">
        <link rel="stylesheet" href="<?php echo e(asset('zaifiles/assets/style.css')); ?>">
    </head>
<body>
<?php echo $__env->yieldContent('preloader'); ?>

<div class="overlay-wrap">
    <div class="breadcrumb-area">
        <div class="container">
            <div class="row">
                <div class="col-xl-12 col-lg-12 col-md-12">
                    <div class="breadcrumb-text">
                        <a class="brand-logo" href="#"><img src="<?php echo e(getSettingImage('app_logo')); ?>" alt="logo"></a>
                        <h2><?php echo e(getOption("app_name")); ?></h2>
                        <p><?php echo e(\Carbon\Carbon::parse(now())->format('l, j F Y')); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="pre-installation-area">
        <div class="container">
            <div class="section-wrap">
                <div class="section-wrap-header">
                    <div class="progres-stype">
                        <form action="<?php echo e(route('process-update')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <?php if(config('app.build_version') == getOption('build_version')): ?>
                                <p class="me-2 my-2"><span class="text-danger">*</span><?php echo e(__('Your application is upto date')); ?></p>
                            <?php else: ?>
                                <p class="me-2 mb-2"><span class="text-danger">*</span> <?php echo e(__('New version')); ?> <?php echo e(config('app.current_version')); ?> </p>
                                <p class="me-2 mb-2"><span class="text-danger">*</span> <?php echo e(__('Current version')); ?> <?php echo e(getOption('current_version', '1.0')); ?> </p>
                                <p class="me-2 mb-2"><span class="text-danger">*</span> <?php echo e(__('Download your database and present script to avoid any errors')); ?>. (Safety first) </p>
                                <p class="me-2 mb-2"><span class="text-danger">*</span> <?php echo e(__('Please click Update now button, may its need sometime')); ?></p>
                                <div class="mt-3">
                                    <div class="single-section">
                                        <h4 class="section-title mb-2"><?php echo e(__('Please enter your Item purchase code and customer email')); ?></h4>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="email"><?php echo e(__('Customer E-mail')); ?></label>
                                                    <input type="email" class="form-control" id="email" name="email" value="<?php echo e(old('email')); ?>" placeholder="<?php echo e(__('<EMAIL>')); ?>" />
                                                </div>
                                                <?php if($errors->has('email')): ?>
                                                    <div class="error text-danger"><?php echo e($errors->first('email')); ?></div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="purchase_code"><?php echo e(__('Item purchase code')); ?></label>
                                                    <input type="text" class="form-control" id="purchase_code" name="purchase_code" value="<?php echo e(old('purchase_code')); ?>" placeholder="<?php echo e(__('31200164-dd02-49ea-baef-3865c90acc123')); ?>" />
                                                </div>
                                                <?php if($errors->has('purchase_code')): ?>
                                                    <div class="error text-danger"><?php echo e($errors->first('purchase_code')); ?></div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="primary-btn next" id="submitNext" type="submit"><?php echo e(__('Update Now')); ?></button>
                                </div>
                            <?php endif; ?>
                        </form>
                    </div>
                </div>
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap core JavaScript -->
<script src="<?php echo e(asset('frontend/assets/vendor/jquery/jquery-3.6.0.min.js')); ?>"></script>
<script src="<?php echo e(asset('frontend/assets/vendor/bootstrap/js/bootstrap.min.js')); ?>"></script>

<?php echo $__env->yieldPushContent('script'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\zaisub\resources\views/zainiklab/installer/version-update.blade.php ENDPATH**/ ?>