<?php

use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\User\OrderController;
use App\Http\Controllers\Api\ApiKeyController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\OrderController as ApiOrderController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Routes publiques
Route::get('/documentation', [App\Http\Controllers\Api\DocumentationController::class, 'index'])->name('api.documentation');

// Routes de callback (sans authentification)
Route::match(array('GET', 'POST'), 'verify', [OrderController::class, 'verify'])->name('payment.verify');
Route::match(array('GET', 'POST'), '/payment/verify', [CheckoutController::class, 'webhook'])->name('payment.webhook.verify');

// Routes protégées par clé API
Route::middleware('api.key')->group(function () {
    
    // Gestion des clés API
    Route::prefix('api-keys')->group(function () {
        Route::get('/', [ApiKeyController::class, 'index']);
        Route::post('/', [ApiKeyController::class, 'store']);
        Route::get('/{id}', [ApiKeyController::class, 'show']);
        Route::put('/{id}', [ApiKeyController::class, 'update']);
        Route::delete('/{id}', [ApiKeyController::class, 'destroy']);
        Route::post('/{id}/regenerate', [ApiKeyController::class, 'regenerate']);
    });

    // Gestion du profil utilisateur
    Route::prefix('user')->group(function () {
        Route::get('/profile', [UserController::class, 'profile']);
        Route::put('/profile', [UserController::class, 'updateProfile']);
        Route::put('/password', [UserController::class, 'changePassword']);
    });

    // Gestion des commandes
    Route::prefix('orders')->group(function () {
        Route::get('/', [ApiOrderController::class, 'index']);
        Route::post('/', [ApiOrderController::class, 'store']);
        Route::get('/stats', [ApiOrderController::class, 'stats']);
        Route::get('/{id}', [ApiOrderController::class, 'show']);
        Route::post('/{id}/cancel', [ApiOrderController::class, 'cancel']);
    });

    // Routes avec permissions spécifiques
    Route::middleware('api.key:write')->group(function () {
        // Routes nécessitant des permissions d'écriture
    });

    Route::middleware('api.key:admin')->group(function () {
        // Routes nécessitant des permissions d'administration
    });
});

// Route de test de l'API
Route::get('/test', function () {
    return response()->json([
        'message' => 'API fonctionnelle',
        'version' => '1.0.0',
        'timestamp' => now()->toISOString()
    ]);
});
