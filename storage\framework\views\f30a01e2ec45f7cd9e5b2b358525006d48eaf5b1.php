<?php $__env->startPush('title'); ?>
    <?php echo e(__('Dashboard')); ?>

<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="px-24 pb-24 position-relative">
        <!-- Info & Add product button -->
        <div class="d-flex justify-content-between align-items-center g-10 flex-wrap pb-20">
            <!-- Left -->
        </div>
        <!-- Block Summary -->
        <div class="row rg-24 pb-24">
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="bg-white bd-one bd-c-stroke-color bd-ra-12 py-30 px-20">
                    <!-- Icon -->
                    <div class="mb-11 w-32 h-32 bd-ra-8 bg-purple-light d-flex justify-content-center align-items-center">
                        <img src="<?php echo e(asset('user/images/icon/user-fill.svg')); ?>" alt="" />
                    </div>
                    <!-- Title -->
                    <h4 class="fs-14 fw-400 lh-24 text-para-text pb-6"><?php echo e(__('Customers')); ?></h4>
                    <!-- Month/Price -->
                    <p class="fs-24 fw-700 lh-24 text-textBlack"><?php echo e($totalCustomer); ?></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="bg-white bd-one bd-c-stroke-color bd-ra-12 py-30 px-20">
                    <!-- Icon -->
                    <div class="mb-11 w-32 h-32 bd-ra-8 bg-purple-light d-flex justify-content-center align-items-center">
                        <img src="<?php echo e(asset('user/images/icon/bell.svg')); ?>" alt="" />
                    </div>
                    <!-- Title -->
                    <h4 class="fs-14 fw-400 lh-24 text-para-text pb-6"><?php echo e(__('Subscriptions')); ?></h4>
                    <!-- Month/Price -->
                    <p class="fs-24 fw-700 lh-24 text-textBlack">
                        <?php echo e($totalSubscription); ?>/<?php echo e(showPrice($totalSubscriptionSales)); ?></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="bg-white bd-one bd-c-stroke-color bd-ra-12 py-30 px-20">
                    <!-- Icon -->
                    <div class="mb-11 w-32 h-32 bd-ra-8 bg-purple-light d-flex justify-content-center align-items-center">
                        <img src="<?php echo e(asset('user/images/icon/dolor.svg')); ?>" alt="" />
                    </div>
                    <!-- Title -->
                    <h4 class="fs-14 fw-400 lh-24 text-para-text pb-6"><?php echo e(__('Monthly Recurring Revenue')); ?></h4>
                    <!-- Month/Price -->
                    <p class="fs-24 fw-700 lh-24 text-textBlack"><?php echo e(showPrice($monthlyRecurringRevenue)); ?></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="bg-white bd-one bd-c-stroke-color bd-ra-12 py-30 px-20">
                    <!-- Icon -->
                    <div class="mb-11 w-32 h-32 bd-ra-8 bg-purple-light d-flex justify-content-center align-items-center">
                        <img src="<?php echo e(asset('user/images/icon/total-sales.svg')); ?>" alt="" />
                    </div>
                    <!-- Title -->
                    <h4 class="fs-14 fw-400 lh-24 text-para-text pb-6"><?php echo e(__('Total Sales')); ?></h4>
                    <!-- Month/Price -->
                    <p class="fs-24 fw-700 lh-24 text-textBlack"><?php echo e(showPrice($totalSales)); ?></p>
                </div>
            </div>
        </div>
        <!-- Table Summary -->
        <div class="row rg-24">
            <div class="col-md-6">
                <div class="bg-white bd-one bd-c-stroke-color bd-ra-12 p-20">
                    <!--  -->
                    <div class="pb-20 d-flex justify-content-between align-items-center flex-wrap g-10">
                        <!--  -->
                        <div class="">
                            <p class="fs-14 fw-400 lh-20 text-para-text"><?php echo e(__('Total Subscription')); ?></p>
                            <h4 class="fs-18 fw-600 lh-28 text-textBlack"><?php echo e($totalSubscription); ?></h4>
                        </div>
                        <!--  -->
                        <div class="text-end">
                            <div class="d-flex justify-content-end align-items-center cg-8 pb-5">
                                <h4 class="fs-16 fw-700 lh-18 text-textBlack ratio-text">0%</h4>
                                <div
                                    class="w-21 h-21 rounded-circle ratio-icon-bg  d-flex justify-content-center align-items-center fs-12 text-white rotate-n-45">
                                    <i class="fa-solid ratio-icon"></i></div>
                            </div>
                            <p class="fs-14 fw-400 lh-20 text-para-text"><?php echo e(__('VS LAST WEEK')); ?></p>
                        </div>
                    </div>
                    <!--  -->
                    <div class="">
                        <div id="activeUserChart"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="bg-white bd-one bd-c-stroke-color bd-ra-12 p-20">
                    <!--  -->
                    <div class="pb-20 d-flex justify-content-between align-items-center flex-wrap g-10">
                        <!--  -->
                        <div class="">
                            <p class="fs-14 fw-400 lh-20 text-para-text"><?php echo e(__('Activity')); ?></p>
                            <h4 class="fs-18 fw-600 lh-28 text-textBlack"><?php echo e(__('Product Sold Out')); ?></h4>
                        </div>
                        <!-- Select -->
                        <div class="flex-grow-0 d-none">
                            <select class="sf-select-two cs-select-1" id="soldOutChart">
                                <?php $__currentLoopData = $subscriptionyearList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($item['year']); ?>"><?php echo e($item['year']); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <!--  -->
                    <div class="">
                        <div id="activityChart"></div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="p-20 bd-one bd-c-stroke-color bd-ra-12 bg-white mb-24">
                    <h4 class="fs-18 fw-600 lh-24 text-textBlack pb-16"><?php echo e(__('Top Selling Plans')); ?></h4>
                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table zTable zTable-last-item-right">
                            <thead>
                                <tr>
                                    <th scope="col">
                                        <div class="min-w-120"><?php echo e(__('Plan Name')); ?></div>
                                    </th>
                                    <th scope="col">
                                        <div><?php echo e(__('Customers')); ?></div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $TopSellingPlans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($plan->plan_name); ?></td>
                                        <td><?php echo e($plan->users_email); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td class="text-center " colspan="2"><?php echo e(__('No Data Found')); ?></td>
                                    </tr>
                                <?php endif; ?>

                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="p-20 bd-one bd-c-stroke-color bd-ra-12 bg-white">
                    <!--  -->
                    <div class="d-flex justify-content-between align-items-center flex-wrap g-10 pb-10">
                        <h4 class="fs-18 fw-600 lh-24 text-textBlack"><?php echo e(__('Monthly Subscriber')); ?></h4>
                        <!-- Select -->
                        <div class="flex-grow-0 d-flex align-items-center g-7">
                            <select class="sf-select-two cs-select-1" id="monthlySubscriber">
                                <?php $__currentLoopData = $subscriptionyearList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($item['year']); ?>"><?php echo e($item['year']); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table zTable zTable-last-item-right" id="monthlySubscriberTable">
                            <thead>
                                <tr>
                                    <th scope="col">
                                        <div><?php echo e(__('Month')); ?></div>
                                    </th>
                                    <th scope="col">
                                        <div><?php echo e(__('Subscriber')); ?></div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $monthlySubscriber; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($item['month']); ?></td>
                                        <td><?php echo e($item['subscriber']); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td class="text-center " colspan="2"><?php echo e(__('No Data Found')); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="p-20 bd-one bd-c-stroke-color bd-ra-12 bg-white mb-24">
                    <h4 class="fs-18 fw-600 lh-24 text-textBlack pb-16"><?php echo e(__('Revenue Daily Stats')); ?></h4>
                    <!-- Table -->
                    <table class="table zTable-1">
                        <tbody>
                            <tr>
                                <td><?php echo e(__("Today's Sale")); ?></td>
                                <td><?php echo e(showPrice($totalSalesAmountToday)); ?></td>
                            </tr>
                            <tr>
                                <td><?php echo e(__("Yesterday's Sale")); ?></td>
                                <td><?php echo e(showPrice($totalSalesAmountYesterday)); ?></td>
                            </tr>
                            <tr>
                                <td><?php echo e(__('Daily Average')); ?></td>
                                <td><?php echo e(showPrice($dailyAverage)); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="p-20 bd-one bd-c-stroke-color bd-ra-12 bg-white">
                    <!--  -->
                    <div class="d-flex justify-content-between align-items-center flex-wrap g-10 pb-10">
                        <h4 class="fs-18 fw-600 lh-24 text-textBlack"><?php echo e(__('Revenue Monthly State')); ?></h4>
                        <!-- Select -->
                        <div class="flex-grow-0">
                            <select class="sf-select-two cs-select-1" id="monthlyRevenue">
                                <?php $__currentLoopData = $subscriptionyearList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($item['year']); ?>"><?php echo e($item['year']); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <!-- Table -->
                    <table class="table zTable zTable-last-item-right" id="monthlyRevenueTable">
                        <thead>
                            <tr>
                                <th scope="col">
                                    <div><?php echo e(__('Month')); ?></div>
                                </th>
                                <th scope="col">
                                    <div><?php echo e(__('Revenue')); ?></div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $monthlyRevenue; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($item['month']); ?></td>
                                    <td><?php echo e(showPrice($item['revenue'])); ?></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td class="text-center " colspan="2"><?php echo e(__('No Data Found')); ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>
    <input type="hidden" id="monthly-subscriber-route" value="<?php echo e(route('user.monthly-subscriber')); ?>">
    <input type="hidden" id="monthly-revenue-route" value="<?php echo e(route('user.monthly-revenue')); ?>">
    <input type="hidden" id="product-sold-out-chart-data-route"
        value="<?php echo e(route('user.product-sold-out-chart-data')); ?>">
    <input type="hidden" id="daily-subscriber-chart-data-route"
        value="<?php echo e(route('user.daily-subscriber-chart-data')); ?>">
<?php $__env->stopSection(); ?>
<?php $__env->startPush('script'); ?>
    <script src="<?php echo e(asset('user/custom/js/dashboard.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('user.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaisub\resources\views/user/dashboard/dashboard.blade.php ENDPATH**/ ?>