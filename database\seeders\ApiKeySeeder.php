<?php

namespace Database\Seeders;

use App\Models\ApiKey;
use App\Models\User;
use Illuminate\Database\Seeder;

class ApiKeySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer des clés API pour les utilisateurs existants
        $users = User::where('role', 'user')->take(5)->get();
        
        foreach ($users as $user) {
            // Clé API de base (lecture seule)
            ApiKey::create([
                'user_id' => $user->id,
                'name' => 'Clé API de base - ' . $user->name,
                'permissions' => ['read'],
                'is_active' => true,
            ]);
            
            // Clé API complète (toutes les permissions)
            ApiKey::create([
                'user_id' => $user->id,
                'name' => 'Clé API complète - ' . $user->name,
                'permissions' => ['read', 'write', 'delete'],
                'is_active' => true,
            ]);
            
            // Clé API expirée (pour test)
            ApiKey::create([
                'user_id' => $user->id,
                'name' => 'Clé API expirée - ' . $user->name,
                'permissions' => ['read'],
                'expires_at' => now()->subDay(),
                'is_active' => true,
            ]);
        }
    }
} 