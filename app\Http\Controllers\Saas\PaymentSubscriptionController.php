<?php

namespace App\Http\Controllers\Saas;

use App\Http\Controllers\Controller;
use App\Http\Requests\CheckoutRequest;
use App\Http\Services\Payment\Payment;
use App\Http\Services\Saas\SubscriptionService;
use App\Models\Bank;
use App\Models\Currency;
use App\Models\EmailTemplate;
use App\Models\FileManager;
use App\Models\Gateway;
use App\Models\GatewayCurrency;
use App\Models\Package;
use App\Models\SubscriptionOrder;
use App\Models\User;
use App\Services\SmsMail\MailService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentSubscriptionController extends Controller
{
    public function checkout(CheckoutRequest $request)
    {
        DB::beginTransaction();
        try {
            // current package check
            $subscriptionService = new SubscriptionService;
            $userPackage = $subscriptionService->getCurrentPackage();
            if (isset($userPackage)) {
                if ($userPackage->package_id == $request->package_id && $userPackage->duration_type == $request->duration_type) {
                    throw new Exception(__('Package Already Exist'));
                }
            }

            $adminUser = User::where('role', USER_ROLE_ADMIN)->first();
            $durationType = $request->duration_type == DURATION_MONTH ? DURATION_MONTH : DURATION_YEAR;
            $package = Package::findOrFail($request->package_id);
            $gateway = Gateway::where(['user_id' => $adminUser->id, 'slug' => $request->gateway, 'status' => ACTIVE])->firstOrFail();
            $gatewayCurrency = GatewayCurrency::where(['user_id' => $adminUser->id, 'gateway_id' => $gateway->id, 'currency' => $request->currency])->firstOrFail();
            if ($gateway->slug == 'bank') {
                $bank = Bank::where(['user_id' => $adminUser->id, 'gateway_id' => $gateway->id, 'id' => $request->bank_id])->first();
                if (is_null($bank)) {
                    throw new Exception(__('Bank not found'));
                }
                $bank_id = $bank->id;
                $bank_deposit_by = $request->deposit_by;
                $bank_deposit_slip_id = null;
                if ($request->hasFile('bank_slip')) {
                    /*File Manager Call upload for Thumbnail Image*/
                    $newFile = new FileManager();
                    $uploaded = $newFile->upload('Order', $request->bank_slip);
                    if ($uploaded) {
                        $bank_deposit_slip_id = $uploaded->id;
                    } else {
                        throw new Exception($uploaded['message']);
                    }
                    /*End*/
                } else {
                    throw new Exception(__('The Bank slip is required'));
                }
                $order = $this->placeOrder($package, $durationType, $gateway, $gatewayCurrency, null, $bank_id, $bank_deposit_by, $bank_deposit_slip_id);
                $order->bank_deposit_slip_id = $bank_deposit_slip_id;
                $order->save();
                DB::commit();
                return redirect()->route('user.subscription.index')->with('success', __('Bank Details Sent Successfully! Wait for approval'));
            } elseif ($gateway->slug == 'cash') {
                $order = $this->placeOrder($package, $durationType, $gateway, $gatewayCurrency);
                $order->save();
                DB::commit();
                return redirect()->route('user.subscription.index')->with('success', __('Cash Payment Request Sent Successfully! Wait for approval'));
            } else {
                $order = $this->placeOrder($package, $durationType, $gateway, $gatewayCurrency);
                DB::commit();
            }
            $object = [
                'id' => $order->id,
                'callback_url' => route('payment.subscription.verify'),
                'cancel_url' => route('payment.subscription.failed'),
                'currency' => $gatewayCurrency->currency,
                'type' => 'subscription'
            ];

            $productPrice = $package->subscriptionPrice->where('gateway_id', $gateway->id)->first();
            $postData =Auth::user();
            if ($productPrice) {
                $object['callback_url'] = route('payment.subscription.verify', ['subscription_success' => true]);
                $payment = new Payment($gateway->slug, $object);
                $planId = $durationType == DURATION_MONTH ? $productPrice->monthly_price_id : $productPrice->yearly_price_id;
                $responseData = $payment->subscribeSaas($planId, ['package_id' => $package->id, 'package_gateway_price_id' => $productPrice->id]);
            } else {
                $payment = new Payment($gateway->slug, $object);
                $responseData = $payment->makePayment($order->total, $postData);
            }

            if ($responseData['success']) {
                $order->payment_id = $responseData['payment_id'];
                $order->save();
                return redirect($responseData['redirect_url']);
            } else {
                return redirect()->back()->with('error', $responseData['message']);
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('user.subscription.index')->with('error', $e->getMessage());
        }
    }

    public function placeOrder($package, $durationType, $gateway, $gatewayCurrency, $userId = null, $bank_id = null, $bank_deposit_by = null, $bank_deposit_slip_id = null)
    {
        $price = 0;
        $discount = 0;
        if ($durationType == DURATION_MONTH) {
            $price = $package->monthly_price;
        } else {
            $price = $package->yearly_price;
        }

        return SubscriptionOrder::create([
            'user_id' => $userId ?? auth()->id(),
            'package_id' => $package->id,
            'order_id' => uniqid(),
            'payment_status' => PAYMENT_STATUS_PENDING,
            'transaction_id' => str_replace("-", "", uuid_create(UUID_TYPE_RANDOM)),
            'system_currency' => Currency::where('current_currency', ACTIVE)->first()->currency_code,
            'gateway_id' => $gateway->id,
            'gateway_currency' => $gatewayCurrency->currency,
            'duration_type' => $durationType,
            'conversion_rate' => $gatewayCurrency->conversion_rate,
            'amount' => $price,
            'tax_amount' => 0,
            'tax_type' => 0,
            'discount' => $discount,
            'subtotal' => $price,
            'total' => $price,
            'transaction_amount' => $price * $gatewayCurrency->conversion_rate,
            'bank_id' => $bank_id,
            'bank_deposit_by' => $bank_deposit_by,
            'bank_deposit_slip_id' => $bank_deposit_slip_id,
        ]);
    }

    public function verify(Request $request)
    {
//        dd($request->token);
        Log::info("*********");
        Log::info($request->all());
        $order_id = $request->get('id', '');
        $payerId = $request->get('PayerID', NULL);
        $payment_id = $request->get('payment_id', NULL);

        $order = SubscriptionOrder::findOrFail($order_id);

        if ($order->status == PAYMENT_STATUS_PAID) {
            return redirect()->route('user.subscription.index')->with('error', __('Your order has been paid!'));
        }


        $gateway = Gateway::find($order->gateway_id);
        if(in_array($gateway->slug,['stripe','paypal'])){
            return redirect()->route('user.subscription.index')->with('success', __('Payment is under Processing!'));
        }

        DB::beginTransaction();
        try {
            if ($order->gateway_id == $gateway->id && $gateway->slug == MERCADOPAGO) {
                $order->payment_id = $payment_id;
                $order->save();
            }

            $payment_id = $order->payment_id;
            $gatewayBasePayment = new Payment($gateway->slug, ['currency' => $order->gateway_currency, 'type' => 'subscription']);
            $payment_data = $gatewayBasePayment->paymentConfirmation($payment_id, $payerId, $request->token);

            if ($payment_data['success']) {

                if ($payment_data['data']['payment_status'] == 'success') {
                    $order->payment_status = PAYMENT_STATUS_PAID;
                    $order->transaction_id = str_replace('-', '', uuid_create());
                    $order->save();
                    $package = Package::find($order->package_id);
                    $duration = 0;
                    if ($order->duration_type == DURATION_MONTH) {
                        $duration = 30;
                    } elseif ($order->duration_type == DURATION_YEAR) {
                        $duration = 365;
                    }

                    setUserPackage($order->user_id, $package, $duration, $order->id);
                    DB::commit();


                    if (auth()->id() == null){
                        Auth::loginUsingId($order->user_id, TRUE);
                    }

                    $adminUser = User::where('status', USER_ROLE_ADMIN)->first();

                    setCommonNotification('Have a new checkout', 'Order Id: ' . $order->order_id, '', $adminUser->id);

                    return redirect()->route('user.subscription.index')->with('success', __('Payment Successful!'));
                }
            } else {
                return redirect()->route('user.subscription.index')->with('error', __('Payment Failed!'));
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('user.subscription.index')->with('error', __('Payment Failed!'));
        }
    }

    public  function failed()
    {
        $data['pageTitle'] = __('Payment Failed');
        return view('user.subscription.failed', $data);
    }


    public function webhook(Request $request)
    {
        // Retrieve the gateway based on the payment method (Stripe or PayPal)
        $userId = User::where('role', USER_ROLE_ADMIN)->first()->id;
        $gateway = Gateway::where(['user_id' => $userId, 'slug' => $request->payment_method])->first();
        $gatewayCurrency = GatewayCurrency::where(['gateway_id' => $gateway->id])->first();

        if (!$gateway) {
            return response()->json(['success' => false, 'message' => 'Gateway not found']);
        }

        // Define the payment service object dynamically
        $object = [
            'type' => 'subscription',
            'currency' => $gatewayCurrency->currency,
        ];

        $paymentService = new Payment($request->payment_method, $object);

        // Handle the webhook request using the respective service (Stripe or PayPal)
        $response = $paymentService->handleWebhook($request);

        if ($response['success']) {
            // Determine whether the event is from Stripe or PayPal and handle it accordingly
            $event = $response['event'];

            Log::info($event);

            if ($request->payment_method === 'stripe') {
                // Call Stripe specific webhook handler
                $this->stripeWebhook($event);
            } elseif ($request->payment_method === 'paypal') {
                // Call PayPal specific webhook handler
                $this->paypalWebhook($event);
            }

            return response()->json(['success' => true, 'message' => 'Webhook handled successfully']);
        } else {
            return response()->json(['success' => false, 'message' => $response['message']]);
        }
    }

    function stripeWebhook($event)
    {
        try {
            DB::beginTransaction();
            // Process the event based on its type
            switch ($event->type) {
                case 'invoice.created':
                    $response = $event->data->object;
                    $metaData = $response->subscription_details->metadata;
                    $planData = $response->lines->data[0]->plan;

                    $packageType = $planData->interval == 'month' ? DURATION_MONTH : DURATION_YEAR;
                    $package = Package::where('id', $metaData->package_id)->first();

                    $price = $planData->interval == 'month' ? $package->monthly_price : $package->yearly_price;

                    if ($price * 100 <= $response->total) {
                        $payment = SubscriptionOrder::where(['user_id' => $metaData->user_id, 'payment_id' => $response->id])->first();
                        if (is_null($payment)) {
                            $userId = User::where('role', USER_ROLE_ADMIN)->first()->id;
                            $gateway = Gateway::where(['user_id' => $userId, 'slug' => STRIPE, 'status' => ACTIVE])->firstOrFail();
                            $gatewayCurrency = GatewayCurrency::where(['gateway_id' => $gateway->id])->first();
                            $order = $this->placeOrder($package, $packageType, $gateway, $gatewayCurrency, $metaData->user_id);
                            $order->payment_id = $response->id;
                            $order->save();
                        } else {
                            Log::info('--------***Already order found***------');
                            Log::info('--------***Check if invoice order already exist END***------');
                        }
                    } else {
                        Log::info('--------***Amount mismatch***------');
                        Log::info('--------***Webhook END***------');
                    }
                    DB::commit();
                    break;
                case 'invoice.payment_succeeded':
                    $response = $event->data->object;
                    $metaData = $response->subscription_details->metadata;
                    //check if the payment is there and in processing
                    Log::info('--------***Check if order exist or order status in processing START***------');
                    $order = SubscriptionOrder::where('payment_id', $response->id)->first();
                    if (!is_null($order) && $order->payment_status == PAYMENT_STATUS_PENDING) {
                        Log::info('--------***Order found***------');
                        Log::info('--------***Order invoice verify START***------');
                        $order->payment_status = PAYMENT_STATUS_PAID;
                        $order->transaction_id = str_replace('-', '', uuid_create());
                        $order->save();
                        $package = Package::find($order->package_id);
                        $duration = 0;
                        if ($order->duration_type == DURATION_MONTH) {
                            $duration = 30;
                        } elseif ($order->duration_type == DURATION_YEAR) {
                            $duration = 365;
                        }

                        setUserPackage($metaData->user_id, $package, $duration, $order->id);
                        $adminUser = User::where('status', USER_ROLE_ADMIN)->first();
                        setCommonNotification('Have a new payment', 'Order Id: ' . $order->order_id, '', $adminUser->id);

                        DB::commit();
                        Log::info('--------***Order invoice verify END***------');
                    } else {
                        Log::info('--------***Order not found with that criteria***------');
                        Log::info('--------***Check if order exist or order status in processing END***------');
                    }
                    DB::commit();
                    break;
                // Add more cases for other event types as needed
                default:
                    // Handle unknown event types
                    break;
            }
        } catch (\Exception $e) {
            DB::rollBack();
            // Invalid payload
            Log::info('Stripe webhook error: ' . $e->getMessage() . ' Line: ' . $e->getLine() . ' File: ' . $e->getFile());
            Log::info('--------***Webhook Failed -- END***------');
        }

    }

    public function paypalWebhook($event)
    {
        // Handle PayPal specific events
        switch ($event['event_type']) {
            case 'PAYMENT.SALE.COMPLETED':
                $resource = $event['resource'];
                Log::info('Handling PayPal Payment Completed:', $resource);

                // Extract payment information from the webhook
                $paymentId = $resource['id'];
                $metaData = json_decode($resource['custom'], true); // Assuming 'custom_id' stores package_id and user_id

                // Find the subscription order using the payment ID or transaction ID
                $order = SubscriptionOrder::where('payment_id', $paymentId)->first();

                if (is_null($order)) {
                    // No order found, create a new one
                    $userId = $metaData['user_id'] ?? null;
                    $packageId = $metaData['package_id'] ?? null;
                    $package = Package::find($packageId);

                    if (is_null($package) || is_null($userId)) {
                        Log::error("Invalid metadata for PayPal event: " . json_encode($metaData));
                        return;
                    }

                    $packageType = $metaData['duration_type'] === 'monthly' ? DURATION_MONTH : DURATION_YEAR;
                    $gateway = Gateway::where(['user_id' => $userId, 'slug' => 'paypal', 'status' => ACTIVE])->firstOrFail();
                    $gatewayCurrency = GatewayCurrency::where(['gateway_id' => $gateway->id])->first();

                    // Create new order
                    $order = $this->placeOrder($package, $packageType, $gateway, $gatewayCurrency, $userId);
                    $order->payment_id = $paymentId;
                    $order->save();
                }

                // If order exists and payment is pending, mark it as paid
                if ($order && $order->payment_status == PAYMENT_STATUS_PENDING) {
                    $order->payment_status = PAYMENT_STATUS_PAID;
                    $order->transaction_id = $paymentId;  // PayPal Transaction ID
                    $order->save();

                    // Activate user package
                    $package = Package::find($order->package_id);
                    $duration = $order->duration_type == DURATION_MONTH ? 30 : 365;
                    setUserPackage($metaData['user_id'], $package, $duration, $order->id);
                    $adminUser = User::where('status', USER_ROLE_ADMIN)->first();
                    setCommonNotification('Have a new payment', 'Order Id: ' . $order->order_id, '', $adminUser->id);

                    Log::info('Payment successfully completed for order ID: ' . $order->id);
                } else {
                    Log::warning('Order not found or already processed for payment ID: ' . $paymentId);
                }
                break;
            default:
                // Handle unknown event types
                break;
        }
    }

}
