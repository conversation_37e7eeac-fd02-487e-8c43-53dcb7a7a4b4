<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Api<PERSON>ey extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'key',
        'permissions',
        'last_used_at',
        'expires_at',
        'is_active'
    ];

    protected $casts = [
        'permissions' => 'array',
        'last_used_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_active' => 'boolean'
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->key)) {
                $model->key = 'sk_' . Str::random(48);
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function hasPermission($permission)
    {
        if (empty($this->permissions)) {
            return false;
        }
        
        return in_array($permission, $this->permissions) || in_array('*', $this->permissions);
    }

    public function isExpired()
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function canUse()
    {
        return $this->is_active && !$this->isExpired();
    }
} 