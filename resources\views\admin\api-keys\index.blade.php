@extends('admin.layouts.master')
@section('title', 'Gestion des Clés API')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-flex align-items-center justify-content-between">
                <h4 class="mb-0">Gestion des Clés API</h4>
                <div class="page-title-right">
                    <a href="{{ route('admin.api-keys.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Nouvelle Clé API
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Utilisateur</th>
                                    <th>Nom</th>
                                    <th>Clé (partielle)</th>
                                    <th>Permissions</th>
                                    <th>Dernière utilisation</th>
                                    <th>Expire le</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($apiKeys as $apiKey)
                                    <tr>
                                        <td>{{ $apiKey->id }}</td>
                                        <td>
                                            <div>
                                                <strong>{{ $apiKey->user->name }}</strong><br>
                                                <small class="text-muted">{{ $apiKey->user->email }}</small>
                                            </div>
                                        </td>
                                        <td>{{ $apiKey->name }}</td>
                                        <td>
                                            <code>{{ substr($apiKey->key, 0, 10) }}...</code>
                                        </td>
                                        <td>
                                            @if($apiKey->permissions)
                                                @foreach($apiKey->permissions as $permission)
                                                    <span class="badge bg-info me-1">{{ $permission }}</span>
                                                @endforeach
                                            @else
                                                <span class="text-muted">Aucune</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($apiKey->last_used_at)
                                                {{ $apiKey->last_used_at->format('d/m/Y H:i') }}
                                            @else
                                                <span class="text-muted">Jamais utilisée</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($apiKey->expires_at)
                                                {{ $apiKey->expires_at->format('d/m/Y H:i') }}
                                                @if($apiKey->isExpired())
                                                    <span class="badge bg-danger ms-1">Expirée</span>
                                                @endif
                                            @else
                                                <span class="text-muted">Jamais</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($apiKey->is_active)
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-secondary">Inactive</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.api-keys.show', $apiKey->id) }}" 
                                                   class="btn btn-sm btn-info" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.api-keys.edit', $apiKey->id) }}" 
                                                   class="btn btn-sm btn-warning" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{{ route('admin.api-keys.regenerate', $apiKey->id) }}" 
                                                   class="btn btn-sm btn-secondary" title="Régénérer"
                                                   onclick="return confirm('Êtes-vous sûr de vouloir régénérer cette clé ?')">
                                                    <i class="fas fa-sync"></i>
                                                </a>
                                                <a href="{{ route('admin.api-keys.toggle', $apiKey->id) }}" 
                                                   class="btn btn-sm btn-{{ $apiKey->is_active ? 'warning' : 'success' }}" 
                                                   title="{{ $apiKey->is_active ? 'Désactiver' : 'Activer' }}">
                                                    <i class="fas fa-{{ $apiKey->is_active ? 'pause' : 'play' }}"></i>
                                                </a>
                                                <a href="{{ route('admin.api-keys.delete', $apiKey->id) }}" 
                                                   class="btn btn-sm btn-danger" title="Supprimer"
                                                   onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette clé ?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">Aucune clé API trouvée</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if($apiKeys->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $apiKeys->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
</script>
@endpush 