<!DOCTYPE html>
<html class="no-js" lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title><?php echo e(getOption('app_name')); ?> - <?php echo $__env->yieldPushContent('title' ?? ''); ?></title>
    <?php if (! empty(trim($__env->yieldContent('meta')))): ?>
        <?php echo $__env->yieldPushContent('meta'); ?>
    <?php else: ?>
        <?php
            $metaData = getMeta('home');
        ?>

            <!-- Open Graph meta tags for social sharing -->
        <meta property="og:type" content="<?php echo e(__('zaisub')); ?>">
        <meta property="og:title" content="<?php echo e($metaData['meta_title'] ?? getOption('app_name')); ?>">
        <meta property="og:description" content="<?php echo e($metaData['meta_description'] ?? getOption('app_name')); ?>">
        <meta property="og:image" content="<?php echo e($metaData['og_image'] ?? getSettingImage('app_logo')); ?>">
        <meta property="og:url" content="<?php echo e(url()->current()); ?>">
        <meta property="og:site_name" content="<?php echo e(getOption('app_name')); ?>">

        <!-- Twitter Card meta tags for Twitter sharing -->
        <meta name="twitter:card" content="<?php echo e(__('zaisub')); ?>">
        <meta name="twitter:title" content="<?php echo e($metaData['meta_title'] ?? getOption('app_name')); ?>">
        <meta name="twitter:description" content="<?php echo e($metaData['meta_description'] ?? getOption('app_name')); ?>">
        <meta name="twitter:image" content="<?php echo e($metaData['og_image'] ?? getSettingImage('app_logo')); ?>">

        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>" />
    <?php endif; ?>
    <link rel="icon" href="<?php echo e(getSettingImage('app_fav_icon')); ?>" type="image/png" sizes="16x16">
    <link rel="shortcut icon" href="<?php echo e(getSettingImage('app_fav_icon')); ?>" type="image/x-icon">
    <link rel="shortcut icon" href="<?php echo e(getSettingImage('app_fav_icon')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/scss/extra-style.css')); ?>" />

    <!-- fonts file -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <?php echo $__env->make('user.layouts.style', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</head>

<body class="<?php echo e(selectedLanguage()->rtl == 1 ? 'direction-rtl' : 'direction-ltr'); ?> <?php echo e(!(getOption('app_color_design_type', DEFAULT_COLOR) == DEFAULT_COLOR) ? 'custom-color' : ''); ?>">
    <!-- Pre Loader Area start -->
    <?php if(getOption('app_preloader_status', 0) == STATUS_ACTIVE): ?>
        <div id="preloader">
            <div id="preloader_status">
                <img src="<?php echo e(getSettingImage('app_preloader')); ?>" alt="<?php echo e(getOption('app_name')); ?>" />
            </div>
        </div>
    <?php endif; ?>
    <?php echo $__env->make('admin.setting.partials.dynamic-color', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!-- Main Content -->
    <div class="zMain-wrap">
        <!-- Sidebar -->
        <?php echo $__env->make('user.layouts.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- Main Content -->
        <div class="zMainContent zMain-header-before-1">
            <!-- Header -->
            <?php echo $__env->make('user.layouts.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- Content -->
            <?php echo $__env->yieldContent('content'); ?>
        </div>
    </div>
    <!-- js file  -->
    <?php echo $__env->make('user.layouts.script', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\zaisub\resources\views/user/layouts/app.blade.php ENDPATH**/ ?>