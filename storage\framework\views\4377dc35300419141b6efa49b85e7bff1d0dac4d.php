<!DOCTYPE html>
<html class="no-js" lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<?php echo $__env->make('admin.layouts.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<body class="<?php echo e(selectedLanguage()->rtl == 1 ? 'direction-rtl' : 'direction-ltr'); ?> <?php echo e(!(getOption('app_color_design_type', DEFAULT_COLOR) == DEFAULT_COLOR) ? 'custom-color' : ''); ?>">
    <div class="overflow-x-hidden">
        <?php if(getOption('app_preloader_status', 0) == STATUS_ACTIVE): ?>
            <div id="preloader">
                <div id="preloader_status">
                    <img src="<?php echo e(getSettingImage('app_preloader')); ?>" alt="<?php echo e(getOption('app_name')); ?>" />
                </div>
            </div>
        <?php endif; ?>

        <!-- Main Content -->
        <div class="zMain-wrap">
            <!-- Sidebar -->
            <?php echo $__env->make('admin.layouts.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- Main Content -->
            <div class="zMainContent">
                <!-- Header -->
                <?php echo $__env->make('admin.layouts.nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- Content -->
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </div>
    </div>
    <?php if(!empty(getOption('cookie_status')) && getOption('cookie_status') == STATUS_ACTIVE): ?>
        <div class="cookie-consent-wrap shadow-lg">
            <?php echo $__env->make('cookie-consent::index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    <?php endif; ?>
    <?php echo $__env->make('admin.layouts.script', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\zaisub\resources\views/admin/layouts/app.blade.php ENDPATH**/ ?>