<?php $__env->startPush('title'); ?>
    <?php echo e($title); ?>

<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="p-30">
        <div class="">
            <h4 class="fs-24 fw-500 lh-34 text-black pb-16"><?php echo e(__($title)); ?></h4>
            <div class="row bd-c-ebedf0 bd-half bd-ra-25 bg-white h-100 p-30">
                <div class="col-lg-12">
                    <div class="customers__area bg-style mb-30">
                        <?php if(isAddonInstalled('SUBSAAS') > 0): ?>
                            <div class="item-title d-flex flex-wrap justify-content-end">
                                <div class="mb-3">
                                    <button class="border-0 fs-15 fw-500 lh-25 text-white py-10 px-26 bg-7f56d9 bd-ra-12"
                                        type="button" id="assignPackage">
                                        <i class="fa fa-plus"></i> <?php echo e(__('Assign Package')); ?>

                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div class="">
                            <table class="table zTable" id="packageUserDataTable">
                                <thead>
                                    <th>
                                        <div><?php echo e(__('SL')); ?></div>
                                    </th>
                                    <th>
                                        <div><?php echo e(__('Name')); ?></div>
                                    </th>
                                    <th>
                                        <div><?php echo e(__('Email')); ?></div>
                                    </th>
                                    <th>
                                        <div class="text-nowrap"><?php echo e(__('Package Name')); ?></div>
                                    </th>
                                    <th>
                                        <div><?php echo e(__('Gateway')); ?></div>
                                    </th>
                                    <th>
                                        <div class="text-nowrap"><?php echo e(__('Start Date')); ?></div>
                                    </th>
                                    <th>
                                        <div class="text-nowrap"><?php echo e(__('End Date')); ?></div>
                                    </th>
                                    <th>
                                        <div class="text-nowrap"><?php echo e(__('Payment Status')); ?></div>
                                    </th>
                                    <th>
                                        <div><?php echo e(__('Status')); ?></div>
                                    </th>
                                    <th>
                                        <div><?php echo e(__('Action')); ?></div>
                                    </th>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if(isAddonInstalled('SUBSAAS') > 0): ?>
        <div class="modal fade" id="assignPackageModal" tabindex="-1" aria-labelledby="assignPackageModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header p-20 border-0 pb-0">
                        <h5 class="modal-title fs-18 fw-600 lh-24 text-1b1c17"><?php echo e(__('Assign Package')); ?></h5>
                        <button type="button" class="w-30 h-30 rounded-circle bd-one bd-c-e4e6eb p-0 bg-transparent"
                            data-bs-dismiss="modal" aria-label="Close"><i class="fa-solid fa-times"></i></button>
                    </div>
                    <form class="ajax reset" action="<?php echo e(route('admin.packages.assign')); ?>" method="post"
                        enctype="multipart/form-data" data-handler="commonResponseForModal">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="gateway" value="cash">
                        <input type="hidden" name="currency" value="<?php echo e(currentCurrencyType()); ?>">
                        <div class="modal-body">
                            <div class="row">
                                <div class="primary-form-group mt-4">
                                    <div class="primary-form-group-wrap">
                                        <label class="form-label"><?php echo e(__('User')); ?>

                                            <span class="text-danger">*</span></label>
                                        <select name="user_id" class="primary-form-control select2">
                                            <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($user->id); ?>">
                                                    <?php echo e($user->name); ?>(<?php echo e($user->email); ?>)</option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="primary-form-group mt-4">
                                    <div class="primary-form-group-wrap">
                                        <label class="form-label"><?php echo e(__('Package')); ?>

                                            <span class="text-danger">*</span></label>
                                        <select name="package_id" class="primary-form-control">
                                            <?php $__currentLoopData = $packages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($package->id); ?>"><?php echo e($package->name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="primary-form-group mt-4">
                                    <div class="primary-form-group-wrap">
                                        <label class="form-label"><?php echo e(__('Duration Type')); ?>

                                            <span class="text-danger">*</span></label>
                                        <select name="duration_type" class="primary-form-control">
                                            <option value="1"><?php echo e(__('Monthly')); ?></option>
                                            <option value="2"><?php echo e(__('Yearly')); ?></option>
                                        </select>
                                    </div>
                                </div>

                                <div class="primary-form-group mt-4">
                                    <div class="primary-form-group-wrap">
                                        <label class="form-label"><?php echo e(__('Payment Gateway')); ?>

                                            <span class="text-danger">*</span></label>
                                        <select name="gateway_id" class="primary-form-control">
                                            <?php $__currentLoopData = $gateways; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gateway): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($gateway->id); ?>"><?php echo e($gateway->title); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="modal-footer border-0 pt-0">
                            <button type="submit"
                                class="m-0 fs-15 border-0 fw-500 lh-25 text-white py-10 px-26 bg-7f56d9 bd-ra-12"><?php echo e(__('Assign')); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="modal fade" id="assignPackageEditModal" tabindex="-1" aria-labelledby="assignPackageEditModalLabel"
             aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">

                </div>
            </div>
        </div>

    <?php endif; ?>
    <input type="hidden" id="packagesUserRoute" value="<?php echo e(route('admin.packages.user')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
    <script src="<?php echo e(asset('admin/custom/js/package.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaisub\resources\views/saas/admin/packages/user.blade.php ENDPATH**/ ?>