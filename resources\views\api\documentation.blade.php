<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation API REST</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            z-index: 1000;
        }
        
        .main-content {
            margin-left: 300px;
            padding: 20px;
        }
        
        .endpoint {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
        }
        
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
        }
        
        .method.get { background: #28a745; }
        .method.post { background: #007bff; }
        .method.put { background: #ffc107; color: #212529; }
        .method.delete { background: #dc3545; }
        
        .url {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .nav-link {
            color: #495057;
            text-decoration: none;
            padding: 8px 16px;
            display: block;
            border-radius: 4px;
            margin: 2px 0;
        }
        
        .nav-link:hover {
            background: #e9ecef;
            color: #212529;
        }
        
        .nav-link.active {
            background: #007bff;
            color: white;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            background: #007bff;
            color: white;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .mobile-toggle {
                display: block !important;
            }
        }
        
        .mobile-toggle {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <button class="mobile-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-code"></i> API Documentation</h4>
            <small>Version 1.0.0</small>
        </div>
        
        <nav class="nav flex-column p-3">
            <a href="#introduction" class="nav-link">Introduction</a>
            <a href="#authentication" class="nav-link">Authentification</a>
            <a href="#api-keys" class="nav-link">Gestion des Clés API</a>
            <a href="#users" class="nav-link">Utilisateurs</a>
            <a href="#orders" class="nav-link">Commandes</a>
            <a href="#errors" class="nav-link">Codes d'Erreur</a>
            <a href="#rate-limiting" class="nav-link">Limitation de Débit</a>
        </nav>
    </div>

    <div class="main-content">
        <div class="container-fluid">
            <!-- Introduction -->
            <section id="introduction">
                <h1>Documentation API REST</h1>
                <p class="lead">Cette documentation décrit l'utilisation de l'API REST pour intégrer votre plateforme SaaS avec notre système.</p>
                
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> Informations importantes</h5>
                    <ul class="mb-0">
                        <li><strong>URL de base :</strong> <code>{{ url('/api') }}</code></li>
                        <li><strong>Version :</strong> 1.0.0</li>
                        <li><strong>Format :</strong> JSON</li>
                        <li><strong>Encodage :</strong> UTF-8</li>
                    </ul>
                </div>
            </section>

            <!-- Authentication -->
            <section id="authentication">
                <h2>Authentification</h2>
                <p>L'API utilise un système d'authentification par clé API. Vous devez inclure votre clé API dans chaque requête.</p>
                
                <h4>Méthodes d'authentification</h4>
                <div class="endpoint">
                    <h5>En-tête X-API-Key (Recommandé)</h5>
                    <div class="code-block">
                        <pre><code>X-API-Key: sk_your_api_key_here</code></pre>
                    </div>
                </div>
                
                <div class="endpoint">
                    <h5>En-tête Authorization (Alternative)</h5>
                    <div class="code-block">
                        <pre><code>Authorization: Bearer sk_your_api_key_here</code></pre>
                    </div>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> Sécurité</h6>
                    <ul class="mb-0">
                        <li>Gardez votre clé API secrète et ne la partagez jamais</li>
                        <li>Utilisez HTTPS pour toutes les requêtes</li>
                        <li>Régénérez votre clé si elle est compromise</li>
                    </ul>
                </div>
            </section>

            <!-- API Keys Management -->
            <section id="api-keys">
                <h2>Gestion des Clés API</h2>
                <p>Ces endpoints permettent de gérer vos clés API directement via l'API.</p>
                
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="url">/api/api-keys</span>
                    <p>Lister toutes vos clés API</p>
                    
                    <h6>Réponse</h6>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "data": [
    {
      "id": 1,
      "name": "Clé pour application mobile",
      "key": "sk_abc123...",
      "permissions": ["read", "write"],
      "last_used_at": "2024-01-15T10:30:00Z",
      "expires_at": null,
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}</code></pre>
                    </div>
                </div>
                
                <div class="endpoint">
                    <span class="method post">POST</span>
                    <span class="url">/api/api-keys</span>
                    <p>Créer une nouvelle clé API</p>
                    
                    <h6>Paramètres</h6>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "name": "Nouvelle clé API",
  "permissions": ["read", "write"],
  "expires_at": "2024-12-31T23:59:59Z"
}</code></pre>
                    </div>
                    
                    <h6>Réponse</h6>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "message": "Clé API créée avec succès",
  "data": {
    "id": 2,
    "name": "Nouvelle clé API",
    "key": "sk_new_generated_key_here",
    "permissions": ["read", "write"],
    "expires_at": "2024-12-31T23:59:59Z",
    "created_at": "2024-01-15T12:00:00Z"
  }
}</code></pre>
                    </div>
                </div>
                
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="url">/api/api-keys/{id}</span>
                    <p>Obtenir les détails d'une clé API spécifique</p>
                </div>
                
                <div class="endpoint">
                    <span class="method put">PUT</span>
                    <span class="url">/api/api-keys/{id}</span>
                    <p>Mettre à jour une clé API</p>
                </div>
                
                <div class="endpoint">
                    <span class="method delete">DELETE</span>
                    <span class="url">/api/api-keys/{id}</span>
                    <p>Supprimer une clé API</p>
                </div>
                
                <div class="endpoint">
                    <span class="method post">POST</span>
                    <span class="url">/api/api-keys/{id}/regenerate</span>
                    <p>Régénérer une clé API</p>
                </div>
            </section>

            <!-- Users -->
            <section id="users">
                <h2>Utilisateurs</h2>
                <p>Gestion du profil utilisateur et des informations personnelles.</p>
                
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="url">/api/user/profile</span>
                    <p>Obtenir le profil de l'utilisateur connecté</p>
                    
                    <h6>Réponse</h6>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "data": {
    "id": 1,
    "uuid": "user_uuid_here",
    "name": "John Doe",
    "email": "<EMAIL>",
    "mobile": "+33123456789",
    "country": "France",
    "city": "Paris",
    "zip_code": "75001",
    "address": "123 Rue de la Paix",
    "currency": "EUR",
    "company_name": "Ma Société",
    "company_country": "France",
    "company_city": "Paris",
    "company_designation": "CEO",
    "company_zip_code": "75001",
    "company_address": "456 Avenue des Champs",
    "email_verified_at": "2024-01-01T00:00:00Z",
    "image": "path/to/image.jpg",
    "role": "user",
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}</code></pre>
                    </div>
                </div>
                
                <div class="endpoint">
                    <span class="method put">PUT</span>
                    <span class="url">/api/user/profile</span>
                    <p>Mettre à jour le profil utilisateur</p>
                    
                    <h6>Paramètres</h6>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "name": "John Doe Updated",
  "mobile": "+33123456789",
  "country": "France",
  "city": "Lyon",
  "zip_code": "69001",
  "address": "789 Rue de la République",
  "currency": "EUR",
  "company_name": "Ma Nouvelle Société",
  "company_country": "France",
  "company_city": "Lyon",
  "company_designation": "CTO",
  "company_zip_code": "69001",
  "company_address": "101 Boulevard de la Croix-Rousse"
}</code></pre>
                    </div>
                </div>
                
                <div class="endpoint">
                    <span class="method put">PUT</span>
                    <span class="url">/api/user/password</span>
                    <p>Changer le mot de passe</p>
                    
                    <h6>Paramètres</h6>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "current_password": "ancien_mot_de_passe",
  "new_password": "nouveau_mot_de_passe",
  "new_password_confirmation": "nouveau_mot_de_passe"
}</code></pre>
                    </div>
                </div>
            </section>

            <!-- Orders -->
            <section id="orders">
                <h2>Commandes</h2>
                <p>Gestion des commandes et des transactions.</p>
                
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="url">/api/orders</span>
                    <p>Lister toutes les commandes de l'utilisateur</p>
                    
                    <h6>Paramètres de requête</h6>
                    <ul>
                        <li><code>page</code> - Numéro de page (défaut: 1)</li>
                        <li><code>per_page</code> - Nombre d'éléments par page (défaut: 20)</li>
                    </ul>
                    
                    <h6>Réponse</h6>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "data": [
    {
      "id": 1,
      "user_id": 1,
      "product_id": 1,
      "plan_id": null,
      "quantity": 1,
      "amount": 99.99,
      "gateway_id": 1,
      "status": "completed",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:35:00Z",
      "product": {
        "id": 1,
        "name": "Produit Premium",
        "price": 99.99
      }
    }
  ],
  "pagination": {
    "current_page": 1,
    "last_page": 5,
    "per_page": 20,
    "total": 100
  }
}</code></pre>
                    </div>
                </div>
                
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="url">/api/orders/{id}</span>
                    <p>Obtenir les détails d'une commande spécifique</p>
                </div>
                
                <div class="endpoint">
                    <span class="method post">POST</span>
                    <span class="url">/api/orders</span>
                    <p>Créer une nouvelle commande</p>
                    
                    <h6>Paramètres</h6>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "product_id": 1,
  "quantity": 2,
  "gateway_id": 1
}</code></pre>
                    </div>
                    
                    <p><strong>OU</strong></p>
                    
                    <div class="code-block">
                        <pre><code class="language-json">{
  "plan_id": 1,
  "quantity": 1,
  "gateway_id": 1
}</code></pre>
                    </div>
                </div>
                
                <div class="endpoint">
                    <span class="method post">POST</span>
                    <span class="url">/api/orders/{id}/cancel</span>
                    <p>Annuler une commande</p>
                </div>
                
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="url">/api/orders/stats</span>
                    <p>Obtenir les statistiques des commandes</p>
                    
                    <h6>Réponse</h6>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "data": {
    "total_orders": 50,
    "pending_orders": 5,
    "completed_orders": 40,
    "cancelled_orders": 5,
    "total_amount": 4999.50
  }
}</code></pre>
                    </div>
                </div>
            </section>

            <!-- Errors -->
            <section id="errors">
                <h2>Codes d'Erreur</h2>
                <p>L'API utilise les codes de statut HTTP standard pour indiquer le succès ou l'échec des requêtes.</p>
                
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Description</th>
                                <th>Exemple</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="badge bg-success">200</span></td>
                                <td>Succès</td>
                                <td>Requête traitée avec succès</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-primary">201</span></td>
                                <td>Créé</td>
                                <td>Ressource créée avec succès</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-danger">400</span></td>
                                <td>Requête incorrecte</td>
                                <td>Paramètres manquants ou invalides</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-danger">401</span></td>
                                <td>Non autorisé</td>
                                <td>Clé API manquante ou invalide</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-danger">403</span></td>
                                <td>Interdit</td>
                                <td>Permissions insuffisantes</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-danger">404</span></td>
                                <td>Non trouvé</td>
                                <td>Ressource introuvable</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-danger">422</span></td>
                                <td>Entité non traitable</td>
                                <td>Erreurs de validation</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-danger">429</span></td>
                                <td>Trop de requêtes</td>
                                <td>Limite de débit dépassée</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-danger">500</span></td>
                                <td>Erreur serveur</td>
                                <td>Erreur interne du serveur</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h4>Format des erreurs</h4>
                <div class="code-block">
                    <pre><code class="language-json">{
  "error": "Description de l'erreur",
  "message": "Message détaillé",
  "errors": {
    "field_name": [
      "Message d'erreur spécifique au champ"
    ]
  }
}</code></pre>
                </div>
            </section>

            <!-- Rate Limiting -->
            <section id="rate-limiting">
                <h2>Limitation de Débit</h2>
                <p>L'API applique des limites de débit pour assurer la stabilité du service.</p>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Limites actuelles</h6>
                    <ul class="mb-0">
                        <li><strong>Requêtes par minute :</strong> 60</li>
                        <li><strong>Requêtes par heure :</strong> 1000</li>
                        <li><strong>Requêtes par jour :</strong> 10000</li>
                    </ul>
                </div>
                
                <h4>En-têtes de limitation</h4>
                <p>Les en-têtes suivants sont inclus dans chaque réponse :</p>
                <ul>
                    <li><code>X-RateLimit-Limit</code> - Limite de requêtes</li>
                    <li><code>X-RateLimit-Remaining</code> - Requêtes restantes</li>
                    <li><code>X-RateLimit-Reset</code> - Timestamp de réinitialisation</li>
                </ul>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> Dépassement de limite</h6>
                    <p>Si vous dépassez la limite, vous recevrez une réponse <code>429 Too Many Requests</code> avec un délai de retry dans l'en-tête <code>Retry-After</code>.</p>
                </div>
            </section>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Active navigation highlighting
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrollY >= (sectionTop - 200)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // Mobile sidebar toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const mobileToggle = document.querySelector('.mobile-toggle');
            
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(event.target) && !mobileToggle.contains(event.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
    </script>
</body>
</html> 