<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;

class SaasAddonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('🚀 Installation automatique de l\'addon SUBSAAS...');
        
        try {
            // Installer l'addon SUBSAAS
            $this->installSaasAddon();
            
            $this->command->info('✅ SUBSAAS installé avec succès !');
            
        } catch (\Exception $e) {
            $this->command->error('❌ Erreur: ' . $e->getMessage());
        }
    }
    
    /**
     * Installer l'addon SUBSAAS
     */
    private function installSaasAddon()
    {
        // Mettre à jour les versions en base de données
        DB::table('settings')->updateOrInsert(
            ['option_key' => 'SUBSAAS_build_version'],
            ['option_value' => '10']
        );
        
        DB::table('settings')->updateOrInsert(
            ['option_key' => 'SUBSAAS_current_version'],
            ['option_value' => '1.9']
        );
        
        // Marquer comme installé
        DB::table('settings')->updateOrInsert(
            ['option_key' => 'SUBSAAS_installed'],
            ['option_value' => '1']
        );
        
        // Nettoyer le cache
        Artisan::call('config:clear');
        Artisan::call('cache:clear');
        
        $this->command->info('📦 Versions mises à jour');
        $this->command->info('🧹 Cache nettoyé');
    }
} 