<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class AffiliateMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (file_exists(storage_path('installed'))) {
            if (auth()->user()->role == USER_ROLE_AFFILIATE) {
                if (auth()->check() && auth()->user()->status != USER_STATUS_ACTIVE) {
                    auth()->logout();
                    return redirect()->route('login')->with('error', __('Your account is inactive. Please contact with admin'));
                }
                return $next($request);
            } else {
                abort('403');
            }
        } else {
            return redirect()->route('ZaiInstaller::pre-install');
        }
    }
}
