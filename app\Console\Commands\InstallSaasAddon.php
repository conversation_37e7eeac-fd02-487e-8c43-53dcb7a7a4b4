<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;

class InstallSaasAddon extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'addon:install-saas {--force : Forcer l\'installation même si déjà installé}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Installer l\'addon SUBSAAS de manière permanente';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🚀 Installation de l\'addon SUBSAAS...');
        
        try {
            // Vérifier si déjà installé
            $currentVersion = getCustomerAddonBuildVersion('SUBSAAS');
            $codeVersion = getAddonCodeBuildVersion('SUBSAAS');
            
            $this->info("Version actuelle en DB: {$currentVersion}");
            $this->info("Version du code: {$codeVersion}");
            
            if ($currentVersion >= $codeVersion && !$this->option('force')) {
                $this->warn('⚠️  L\'addon SUBSAAS semble déjà être installé.');
                if (!$this->confirm('Voulez-vous forcer la réinstallation ?')) {
                    return 0;
                }
            }
            
            // Installation de l'addon
            $this->info('📦 Installation en cours...');
            
            // Mettre à jour les versions
            setCustomerAddonBuildVersion('SUBSAAS', $codeVersion);
            setCustomerAddonCurrentVersion('SUBSAAS');
            
            // Exécuter les migrations si nécessaire
            $this->info('🔄 Exécution des migrations...');
            Artisan::call('migrate', ['--force' => true]);
            
            // Nettoyer le cache
            $this->info('🧹 Nettoyage du cache...');
            Artisan::call('config:clear');
            Artisan::call('cache:clear');
            Artisan::call('view:clear');
            Artisan::call('route:clear');
            
            $this->info('✅ SUBSAAS installé avec succès !');
            $this->info('✅ Vous pouvez maintenant accéder à votre panneau d\'administration.');
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de l\'installation: ' . $e->getMessage());
            return 1;
        }
    }
} 