<style>
    :root {
        <?php if(getOption('app_color_design_type', DEFAULT_COLOR) == DEFAULT_COLOR): ?>
        --main-color: #7f56d9;
        --hover-color: #e5f0fb;
        --text-black: #1b1c17;
        --header-color: #f2f4f7;
        --body-bg: #fbf9f1;

        <?php else: ?>
         --main-color: <?php echo e(getOption('main_color','#007aff')); ?>;
        --hover-color: <?php echo e(getOption('hover_color','#e5f0fb')); ?>;
        --text-black: <?php echo e(getOption('text_color','#5d697a')); ?>;
        --header-color: <?php echo e(getOption('header_footer_color','#f2f4f7')); ?>;
        --body-bg: <?php echo e(getOption('sidebar_bg_color','#f9fafb')); ?>;
        --sidebar-bg: <?php echo e(getOption('sidebar_bg','#e5f0fb')); ?>;
        --datatable-header-color: <?php echo e(getOption('datatable_header_color','#e5f0fb')); ?>;

        <?php endif; ?>

       --primary-color: #cdef84;
        --stroke-color: #e4e6eb;
        --bColor: #707070;
        --colorOne: #707070;
        --colorTwo: #ebedf0;
        --colorThree: #fafafa;
        --colorFour: #71e3ba;
        --colorFive: #ed84ef;
        --colorSix: #84a2ef;
        --colorSeven: #f4f4ef;
        --colorEight: #ea4335;
        --colorEight-10: rgb(234 67 53 / 10%);
        --colorNine: #f9f9f9;
        --colorTen: #fdedeb;
        --colorEleven: #eaeaea;
        --colorTwelve: #0fa958;
        --colorTwelve-10: rgb(15 169 88 / 10%);
        --color13: #f5b40a;
        --color13-10: rgb(245 180 10 / 10%);
        --color14: #ebe7d5;
        --color15: #e6ef84;
        --color16: #84dcef;
        --color17: #eef0f2;
        --color18: #b7bdc6;
        --color19: #596680;
        --color20: #f0f0f0;
        --color21: #ed0006;
        --color22: #8d84ef;
        --color23: #d3d9e5;
        --color24: #cdffc5;
        --scroll-track: #efefef;
        --scroll-thumb: #dadada;
        --text-black-50: rgb(27 28 23 / 50%);
        --green: #4cbf4c;
        --red: #f02e17;
        --bg-one: #ededed;
        --border-color: #ededed;
        --border-color-one: #e5e8ec;
        --border-color-deep: #b0b0b0;
        --white: #ffffff;
        --white-10: rgb(255 255 255 / 10%);
        --white-32: rgb(255 255 255 / 32%);
        --white-70: rgb(255 255 255 / 70%);
        --black: #000000;
        --black-5: rgb(0 0 0 / 5%);
        --black-10: rgb(0 0 0 / 10%);
    }
</style>
<?php /**PATH C:\xampp\htdocs\zaisub\resources\views/admin/setting/partials/dynamic-color.blade.php ENDPATH**/ ?>