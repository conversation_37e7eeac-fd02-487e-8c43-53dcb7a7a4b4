<?php

namespace App\Http\Controllers;

use App\Http\Services\GatewayService;
use App\Http\Services\Payment\Payment;
use App\Http\Services\SettingsService;
use App\Models\Bank;
use App\Models\Currency;
use App\Models\FileManager;
use App\Models\Gateway;
use App\Models\GatewayCurrency;
use App\Models\Invoice;
use App\Models\Order;
use App\Models\Subscription;
use App\Models\TaxSetting;
use App\Traits\ResponseTrait;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InvoiceOrderController extends Controller
{
    use ResponseTrait;

    public $gatewayService, $settingsService;

    public function __construct()
    {
        $this->settingsService = new SettingsService();
        $this->gatewayService = new GatewayService();
    }

    public function checkout($hash)
    {
        $paramData = decrypt($hash);

        $data['invoice'] = Invoice::where('id', $paramData['id'])->where('payment_status', PAYMENT_STATUS_PENDING)->with(['plan', 'customer'])->first();
        if (!$data['invoice']) {
            return back()->with('error', __(SOMETHING_WENT_WRONG));
        }

        $tax = 0;
        $price = $data['invoice']->amount;

        $taxSetting = TaxSetting::where('product_id', $data['invoice']->product_id)->where('plan_id', $data['invoice']->plan_id)->first();

        if (!is_null($taxSetting)) {
            if ($taxSetting->tax_type == TAX_TYPE_FLAT) {
                $tax = $taxSetting->tax_amount;
            } else {
                $tax = $price * $taxSetting->tax_amount * 0.01;
            }
        }

        $data['taxAmount'] = $tax;
        $data['pageTitle'] = __('Invoice Pay');
        $data['checkoutPage'] = $this->settingsService->checkoutPageSettingByUserId($paramData['user_id']);
        $data['gateways'] = $this->gatewayService->getActiveAll($paramData['user_id']);
        $data['banks'] = $this->gatewayService->getActiveBanks($paramData['user_id']);

        return view('frontend.invoice-checkout', $data);
    }

    public function pay(Request $request)
    {
        DB::beginTransaction();
        try {
            if (is_null($request->gateway) || is_null($request->currency)) {
                throw new Exception(__('Please select gateway or currency'));
            }

            $checkout_details = decrypt($request->checkout_details);
            $userId = $checkout_details['user_id'];
            $invoiceId = $checkout_details['id'];
            $invoice = Invoice::where('user_id', $userId)->findOrFail($invoiceId);
            $gateway = Gateway::where(['user_id' => $userId, 'slug' => $request->gateway, 'status' => ACTIVE])->firstOrFail();
            $gatewayCurrency = GatewayCurrency::where(['user_id' => $userId, 'gateway_id' => $gateway->id, 'currency' => $request->currency])->firstOrFail();
            $data['gateway'] = $request->gateway;

            $user = $invoice->customer;

            $tax = 0;
            $price = $invoice->amount;

            $taxSetting = TaxSetting::where('product_id', $invoice->product_id)->where('plan_id', $invoice->plan_id)->first();

            if (!is_null($taxSetting)) {
                if ($taxSetting->tax_type == TAX_TYPE_FLAT) {
                    $tax = $taxSetting->tax_amount;
                } else {
                    $tax = $price * $taxSetting->tax_amount * 0.01;
                }
            }

            $subtotal = $invoice->amount + $tax;
            $order = new Order();
            $order->user_id = $invoice->user_id;
            $order->order_id = uniqid();
            $order->customer_id = $user->id;
            $order->product_id = $invoice->product_id;
            $order->plan_id = $invoice->plan_id;
            $order->invoice_id = $invoice->id;
            $order->gateway_id = $gateway->id;
            $order->subscription_id = $invoice->id;
            $order->shipping_cost = $invoice->shipping_charge;
            $order->setup_fees = 0;
            $order->tax_amount = $tax;
            $order->tax_type = $taxSetting?->tax_type ?? TAX_TYPE_FLAT;
            $order->conversion_rate = $gatewayCurrency->conversion_rate;
            $order->payment_status = PAYMENT_STATUS_PENDING;
            $order->system_currency = Currency::where('current_currency', STATUS_ACTIVE)->first()->currency_code;
            $order->gateway_currency = $gatewayCurrency->currency;

            $order->amount = $price;
            $order->subtotal = $subtotal;
            $order->total = $subtotal;
            $order->transaction_amount = $order->total * $gatewayCurrency->conversion_rate;
            $order->save();

            if ($gateway->slug == 'bank') {
                $bank = Bank::where(['user_id' => $userId, 'gateway_id' => $gateway->id])->find($request->bank_id);
                if (is_null($bank)) {
                    throw new Exception(__('The bank not found'));
                }
                $bank_id = $bank->id;
                $deposit_by = $user->name;
                $deposit_slip_id = null;
                if ($request->hasFile('bank_slip')) {
                    $newFile = new FileManager();
                    $uploaded = $newFile->upload('Order', $request->bank_slip);
                    if ($uploaded) {
                        $deposit_slip_id = $uploaded->id;
                    }
                } else {
                    throw new Exception(__('The bank slip is required'));
                }
                $order->bank_id = $bank_id;
                $order->bank_deposit_by = $deposit_by;
                $order->bank_deposit_slip_id = $deposit_slip_id;
                $order->payment_id = uniqid('BNK');
                $order->save();
                DB::commit();
                $message = __('Bank Details Sent Successfully! Wait for approval');
                return $this->success($data, $message);
            } elseif ($gateway->slug == 'cash') {
                $order->payment_id = uniqid('CAS');
                $order->save();
                DB::commit();
                $message = __('Cash Payment Request Sent Successfully! Wait for approval');
                return $this->success($data, $message);
            } else {
                $object = [
                    'id' => $order->id,
                    'callback_url' => route('invoice.verify'),
                    'cancel_url' => route('failed'),
                    'currency' => $gatewayCurrency->currency,
                    'user_id' => $userId,
                ];

                $payment = new Payment($gateway->slug, $object);
                $postData = $user;
                $responseData = $payment->makePayment($order->total, $postData);

                if ($responseData['success']) {
                    $order->payment_id = $responseData['payment_id'];
                    $order->save();
                    $data['redirect_url'] = $responseData['redirect_url'];
                    DB::commit();
                    return $this->success($data);
                } else {
                    throw new Exception($responseData['message']);
                }
            }
        } catch (Exception $e) {
            DB::rollBack();
            $message = getErrorMessage($e, $e->getMessage());
            return $this->error([], $message);
        }
    }

    public function verify(Request $request)
    {
        $order_id = $request->get('id', '');
        $payerId = $request->get('PayerID', NULL);
        $payment_id = $request->get('payment_id', NULL);

        $order = Order::findOrFail($order_id);
        if ($order->payment_status == PAYMENT_STATUS_PAID) {
            return redirect()->route('thankyou');
        }

        $gateway = Gateway::find($order->gateway_id);
        $settingsService = new SettingsService();
        DB::beginTransaction();
        try {
            if ($order->gateway_id == $gateway->id && $gateway->slug == MERCADOPAGO) {
                $order->payment_id = $payment_id;
                $order->save();
            }

            $subscription = Subscription::find($order->subscription_id);
            $invoice = Invoice::where('subscription_id', $subscription->id)->first();

            $gatewayBasePayment = new Payment($gateway->slug, ['currency' => $order->gateway_currency, 'user_id' => $subscription->user_id]);
            $payment_data = $gatewayBasePayment->paymentConfirmation($order->payment_id, $payerId);
            Log::info($payment_data);
            if ($payment_data['success']) {
                if ($payment_data['data']['payment_status'] == 'success') {
                    // subscription
                    if ($subscription->duration == DURATION_MONTH) {
                        $end_date = now()->addDays(30)->format('Y-m-d');
                    } else {
                        $end_date = now()->addDays(365)->format('Y-m-d');
                    }

                    $subscription->status = STATUS_ACTIVE;
                    $subscription->end_date = $end_date;
                    $subscription->save();
                    // invoice
                    $invoice->payment_status = PAYMENT_STATUS_PAID;
                    $invoice->save();
                    // order
                    $order->payment_status = PAYMENT_STATUS_PAID;
                    $order->delivery_status = DELIVERY_STATUS_DELIVERED;
                    $order->transaction_id = uniqid();
                    $order->save();

                    DB::commit();

                    //notification call start
                    setCommonNotification('Have a new checkout', 'Order Id: ' . $order->order_id, '', $userId = $subscription->user_id);

                    // send success mail
                    $settingsService->sendPaymentMail($subscription, $invoice, $order, EMAIL_TEMPLATE_PAYMENT_SUCCESS);
                    return redirect()->route('thankyou');
                }
            } else {
                // send failure mail
                $settingsService->sendPaymentMail($subscription, $invoice, $order, EMAIL_TEMPLATE_PAYMENT_FAILURE);
                return redirect()->route('failed');
            }
        } catch (Exception $e) {
            DB::rollBack();
            return redirect()->route('failed');
        }
    }
}
