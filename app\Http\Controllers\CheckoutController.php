<?php

namespace App\Http\Controllers;

use App\Http\Requests\CheckoutOrderPlaceRequest;
use App\Http\Services\CheckoutService;
use App\Http\Services\GatewayService;
use App\Http\Services\Payment\Payment;
use App\Http\Services\PlanService;
use App\Http\Services\SettingsService;
use App\Models\Gateway;
use App\Models\GatewayCurrency;
use App\Models\Invoice;
use App\Models\InvoiceSetting;
use App\Models\License;
use App\Models\Order;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\TaxSetting;
use App\Models\User;
use App\Traits\ResponseTrait;
use DateTime;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckoutController extends Controller
{
    use ResponseTrait;
    public $settingsService, $gatewayService, $checkoutService, $planService;

    public function __construct()
    {
        $this->settingsService = new SettingsService();
        $this->gatewayService = new GatewayService();
        $this->checkoutService = new CheckoutService();
        $this->planService = new PlanService();
    }

    public function checkout($hash)
    {
        $paramData = decrypt($hash);
        $data['plan'] = $this->planService->details($paramData['plan_id'], $paramData['user_id']);
        if (!$data['plan']) {
            return back()->with('error', __(SOMETHING_WENT_WRONG));
        }

        if($data['plan']->status != STATUS_ACTIVE || $data['plan']->product?->status != STATUS_ACTIVE){
            return back()->with('error', __('The product/plan is inactive'));
        }

        $tax = 0;
        $price = $data['plan']->price;

        $taxSetting = TaxSetting::where('product_id', $data['plan']->product_id)->where('plan_id', $data['plan']->id)->first();

        if (!is_null($taxSetting)) {
            if ($taxSetting->tax_type == TAX_TYPE_FLAT) {
                $tax = $taxSetting->tax_amount;
            } else {
                $tax = $price * $taxSetting->tax_amount * 0.01;
            }
        }

        $data['pageTitle'] = __('Checkout');
        $data['taxAmount'] = $tax;
        $data['checkoutPage'] = $this->settingsService->checkoutPageSettingByUserId($paramData['user_id']);
        $data['gateways'] = $this->gatewayService->getActiveAll($paramData['user_id']);
        $data['banks'] = $this->gatewayService->getActiveBanks($paramData['user_id']);

        return view('frontend.checkout', $data);
    }

    public function checkoutOrder(CheckoutOrderPlaceRequest $request)
    {
        return $this->checkoutService->checkoutOrder($request);
    }

    public function getCurrencyByGateway(Request $request)
    {
        $data =  $this->checkoutService->getCurrencyByGatewayId($request->id);
        return $this->success($data);
    }

    public function getCouponInfo(Request $request)
    {
        return $this->settingsService->getCouponInfo($request);
    }
    public function webhook(Request $request)
    {
        // Define the payment service object dynamically
        Log::info("webhook call");

        $gateway = Gateway::where(['slug' => $request->payment_method])->first();
        $gatewayCurrency = GatewayCurrency::where(['gateway_id' => $gateway?->id])->first();
        if (!is_null($gateway) && !is_null($gatewayCurrency)) {
            $object = [
                'webhook_url' => route('payment.webhook.verify', ['payment_method' => $gateway->slug, 'uid' => $request->uid]),
                'currency' => $gatewayCurrency->currency,
                'type' => 'plan',
                'user_id' => $request->uid,
            ];
        }else{
            Log::info('Gateway Not found');
        }

        Log::info($request->payment_method);
        Log::info($request->uid);

        $paymentService = new Payment($request->payment_method, $object);
        Log::info($request->payment_method);
        // Handle the webhook request using the respective service (Stripe or PayPal)
        $response = $paymentService->handleWebhook($request);
        Log::info($response);
        if ($response['success']) {
            // Determine whether the event is from Stripe or PayPal and handle it accordingly
            $event = $response['event'];

            if ($request->payment_method === 'stripe') {
                // Call Stripe specific webhook handler
                Log::info($request->payment_method);
                return $this->stripeWebhook($event);
            } elseif ($request->payment_method === 'paypal') {
                // Call PayPal specific webhook handler
                return $this->paypalWebhook($event);
            }

            return response()->json(['success' => true, 'message' => 'Webhook handled successfully']);
        } else {
            return response()->json(['success' => false, 'message' => $response['message']]);
        }
    }
    function calculateEndDate($interval, $interval_count) {
        $currentDate = new DateTime(); // Current date
        if ($interval === 'month') {
            $currentDate->modify("+{$interval_count} months");
        } elseif ($interval === 'year') {
            $currentDate->modify("+{$interval_count} years");
        } else {
            throw new \Exception("Invalid interval: Only 'month' and 'year' are supported.");
        }

        return $currentDate->format('Y-m-d'); // Format the date as required
    }

    function stripeWebhook($event)
    {
        try {
            Log::info($event->type);
            DB::beginTransaction();
            // Process the event based on its type
            switch ($event->type) {
                case 'invoice.created':
                    $response = $event->data->object;
                    $metaData = $response->subscription_details->metadata;
                    $planData = $response->lines->data[0]->plan;
                    $subscriptionData = Subscription::find($metaData->subscription_id);
                    $price = $subscriptionData->amount;

                    if ($price * 100 <= $response->total) {
                        Log::info("price Matched");
                        $invoice = Invoice::where(['payment_id' => $response->id])->first();
                        if(is_null($invoice)){
                            $dataObj = new Invoice();
                            $dataObj->payment_id = $response->id;
                            $dataObj->user_id = $subscriptionData->user_id;
                            $dataObj->customer_id = $subscriptionData->customer_id;
                            $dataObj->product_id = $subscriptionData->product_id;
                            $dataObj->plan_id = $subscriptionData->plan_id;
                            $dataObj->subscription_id = $subscriptionData->id;
                            $dataObj->due_date = now()->addDays($subscriptionData->due_day)->format('Y-m-d');
                            $dataObj->amount = $subscriptionData->amount;
                            $dataObj->setup_fees = $subscriptionData->setup_fee ?? 0;
                            $dataObj->shipping_charge = $subscriptionData->shipping_charge;
                            $dataObj->is_recurring = 1;
                            $dataObj->save();
                            if ($dataObj->id) {
                                $invoiceSettingData = InvoiceSetting::where('user_id', $dataObj->user_id)->first();
                                $invoiceId = (isset($invoiceSettingData->prefix) && $invoiceSettingData->prefix != null) ? $invoiceSettingData->prefix . sprintf('%06d', $dataObj->id) : 'INV' . sprintf('%06d', $dataObj->id);
                                Invoice::where('id', $dataObj->id)->update(['invoice_id' => $invoiceId]);
                            }
                            DB::commit();
                            return [
                                'success' => true,
                                'message' => "success"
                            ];
                        } else {
                            Log::info('--------***Already Invoice found***------');
                            Log::info('--------***Check if invoice order already exist END***------');
                            DB::rollBack();
                            return [
                                'success' => false,
                                'message' => "Already Invoice found"
                            ];
                        }
                    } else {
                        Log::info('--------***Amount mismatch***------');
                        Log::info('--------***Webhook END***------');
                        DB::rollBack();
                        return [
                            'success' => false,
                            'message' => "Amount mismatch"
                        ];
                    }
                    break;
                case 'invoice.payment_succeeded':
                    $response = $event->data->object;
                    $metaData = $response->subscription_details->metadata;
                    foreach ($response->lines->data as $d){
                        if($d->type == 'subscription'){
                            $planData = $d->plan;
                        }
                    }
//                    Log::info($planData);
                    $invoice = Invoice::where(['payment_id' => $response->id])->first();
                    //check if the payment is there and in processing
                    Log::info('--------***Check if order exist or order status in processing START***------');
                    if (!is_null($invoice) && $invoice->payment_status == STATUS_PENDING) {
                        $subscription = Subscription::find($metaData->subscription_id);
                        $order = Order::find($metaData->order_id);
//                        $licenseData = License::where('product_plan', $subscription->plan_id)->first();
//                        if (!is_null($licenseData)) {
//                            $subscription->license = $licenseData->code . str_replace('-', '', uuid_create(UUID_TYPE_RANDOM));
//                        }

                        $subscription->status = STATUS_ACTIVE;
                        $subscription->end_date = $this->calculateEndDate($planData->interval,$planData->interval_count);
                        $subscription->save();

                        $invoice->payment_status = PAYMENT_STATUS_PAID;
                        $invoice->save();

                        $order->payment_status = PAYMENT_STATUS_PAID;
                        $order->delivery_status = DELIVERY_STATUS_DELIVERED;
                        $order->transaction_id = uniqid();
                        $order->save();

                        //webhook event start
                        $webhookRequestData = [
                            'order_info' => $order,
                            'payment_info' => $response,
                        ];
                        createWebhookEvent(WEBHOOK_EVENT_TYPE_PAYMENT, $subscription->plan_id, $subscription->user_id, $webhookRequestData);
                        //webhook event end

                        //notification call start
                        setCommonNotification('Have a new checkout', 'Order Id: ' . $order->order_id, '', $userId = $subscription->user_id);
                        //notification call end
                        if (getOption('affiliate_status') == STATUS_ACTIVE) {
                            affiliateCommission($invoice);
                        }
                        $settingsService = new SettingsService();
                        // send success mail
                        $settingsService->sendPaymentMail($subscription, $invoice, $order, EMAIL_TEMPLATE_PAYMENT_SUCCESS);
                        DB::commit();
                        Log::info('--------***Order invoice verify END***------');
                        return [
                            'success' => true,
                            'message' => "success"
                        ];
                    } else {
                        DB::rollBack();
                        Log::info('--------***Order not found with that criteria***------');
                        Log::info('--------***Check if order exist or order status in processing END***------');
                        return [
                            'success' => false,
                            'message' => "Order not found with that criteria"
                        ];
                    }
                    break;
                // Add more cases for other event types as needed
                default:
                    // Handle unknown event types
                    break;
            }
            return [
                'success' => true,
                'message' => "success"
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            // Invalid payload
            Log::info('Stripe webhook error: ' . $e->getMessage() . ' Line: ' . $e->getLine() . ' File: ' . $e->getFile());
            Log::info('--------***Webhook Failed -- END***------');
        }

    }

    public function paypalWebhook($event)
    {
        try {
            Log::info('PayPal Webhook Event Type:', [$event['event_type']]);
            DB::beginTransaction();

            // Process the event based on its type
            switch ($event['event_type']) {
                case 'BILLING.SUBSCRIPTION.CREATED':
                    $response = $event['resource'];
                    $customId = isset($response['custom_id']) ? json_decode($response['custom_id'], true) : null;

                    if (!$customId) {
                        throw new Exception('Missing custom_id in BILLING.SUBSCRIPTION.CREATED event.');
                    }

                    Log::info('Processing BILLING.SUBSCRIPTION.CREATED with Custom ID:', $customId);

                    // Log or perform additional actions if needed.
                    DB::commit();
                    return response()->json(['success' => true, 'message' => 'Subscription creation logged successfully.'], 200);

                case 'PAYMENT.SALE.COMPLETED':
                    $response = $event['resource'];
                    $customId = isset($response['custom']) ? json_decode($response['custom'], true) : null;
                    $transactionId = $response['id']; // PayPal transaction ID
                    $amount = $response['amount']['total']; // Payment amount

                    if (!$customId || !isset($customId['subscription_id']) || !isset($customId['order_id'])) {
                        throw new Exception('Missing subscription_id or order_id in custom for PAYMENT.SALE.COMPLETED event.');
                    }

                    Log::info('Processing PAYMENT.SALE.COMPLETED for Subscription ID:', [$customId['subscription_id']]);

                    // Find the subscription using subscription_id from custom
                    $subscription = Subscription::find($customId['subscription_id']);
                    if (!$subscription) {
                        throw new Exception('Subscription not found for Subscription ID: ' . $customId['subscription_id']);
                    }

                    // Find the order using order_id from custom
                    $order = Order::find($customId['order_id']);
                    if (!$order) {
                        throw new Exception('Order not found for Order ID: ' . $customId['order_id']);
                    }

                    // Ensure the invoice doesn't already exist
                    $invoice = Invoice::where('payment_id', $transactionId)->first();
                    if (is_null($invoice)) {
                        $invoice = new Invoice();
                        $invoice->payment_id = $transactionId;
                        $invoice->user_id = $subscription->user_id;
                        $invoice->customer_id = $subscription->customer_id;
                        $invoice->product_id = $subscription->product_id;
                        $invoice->plan_id = $subscription->plan_id;
                        $invoice->subscription_id = $subscription->id;
                        $invoice->due_date = now()->addDays($subscription->due_day)->format('Y-m-d');
                        $invoice->amount = $amount;
                        $invoice->setup_fees = $subscription->setup_fee ?? 0;
                        $invoice->shipping_charge = $subscription->shipping_charge ?? 0;
                        $invoice->is_recurring = 1;
                        $invoice->payment_status = PAYMENT_STATUS_PAID;
                        $invoice->save();

                        // Generate the invoice ID with prefix
                        $invoiceSetting = InvoiceSetting::where('user_id', $invoice->user_id)->first();
                        $invoiceId = isset($invoiceSetting->prefix) && $invoiceSetting->prefix
                            ? $invoiceSetting->prefix . sprintf('%06d', $invoice->id)
                            : 'INV' . sprintf('%06d', $invoice->id);
                        $invoice->update(['invoice_id' => $invoiceId]);

                        // Update subscription
                        $subscription->status = STATUS_ACTIVE;
                        $subscription->end_date = now()->addMonths($subscription->duration);
                        $subscription->save();

                        // Update order
                        $order->payment_status = PAYMENT_STATUS_PAID;
                        $order->delivery_status = DELIVERY_STATUS_DELIVERED;
                        $order->transaction_id = $transactionId;
                        $order->save();

                        // Additional updates like notifications or affiliate commissions
                        setCommonNotification('Payment Received', 'Order Id: ' . $order->order_id, '', $subscription->user_id);

                        if (getOption('affiliate_status') == STATUS_ACTIVE) {
                            affiliateCommission($invoice);
                        }

                        // Email the user
                        $settingsService = new SettingsService();
                        $settingsService->sendPaymentMail($subscription, $invoice, $order, EMAIL_TEMPLATE_PAYMENT_SUCCESS);

                        Log::info('Payment processed, subscription and order updated successfully.');
                    } else {
                        Log::info('Invoice already exists for Transaction ID: ' . $transactionId);
                    }

                    DB::commit();
                    return response()->json(['success' => true, 'message' => 'Payment processed successfully.'], 200);

                default:
                    Log::info('Unhandled PayPal Event:', [$event['event_type']]);
                    break;
            }

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Event processed successfully.'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('PayPal Webhook Error:', ['message' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
