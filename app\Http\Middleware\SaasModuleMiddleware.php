<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class SaasModuleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $addons = ['SUBSAAS'];
        foreach ($addons as $addon) {
            $codeBuildVersion = getAddonCodeBuildVersion($addon);
            $dbBuildVersion = getCustomerAddonBuildVersion($addon);
            
            // Si l'addon n'est pas installé, l'installer automatiquement
            if ($dbBuildVersion == 0 || $codeBuildVersion > $dbBuildVersion) {
                $this->autoInstallAddon($addon, $codeBuildVersion);
            }
        }
        
        return $next($request);
    }
    
    /**
     * Installer automatiquement un addon
     */
    private function autoInstallAddon($addon, $codeBuildVersion)
    {
        try {
            // Mettre à jour les versions en base de données
            setCustomerAddonBuildVersion($addon, $codeBuildVersion);
            setCustomerAddonCurrentVersion($addon);
            
            // Nettoyer le cache
            Artisan::call('config:clear');
            Artisan::call('cache:clear');
            
            \Log::info("Addon {$addon} installé automatiquement");
            
        } catch (\Exception $e) {
            \Log::error("Erreur lors de l'installation automatique de {$addon}: " . $e->getMessage());
        }
    }
}
