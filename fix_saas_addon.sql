-- Script SQL pour corriger le problème SUBSAAS Addon
-- Exécutez ce script directement dans votre base de données MySQL

-- Vérifier si les enregistrements existent
SELECT * FROM settings WHERE option_key IN ('SUBSAAS_build_version', 'SUBSAAS_current_version');

-- <PERSON><PERSON> à jour ou insérer la version build
INSERT INTO settings (option_key, option_value) 
VALUES ('SUBSAAS_build_version', '10')
ON DUPLICATE KEY UPDATE option_value = '10';

-- <PERSON>tre à jour ou insérer la version courante
INSERT INTO settings (option_key, option_value) 
VALUES ('SUBSAAS_current_version', '1.9')
ON DUPLICATE KEY UPDATE option_value = '1.9';

-- Vérifier que les mises à jour ont fonctionné
SELECT * FROM settings WHERE option_key IN ('SUBSAAS_build_version', 'SUBSAAS_current_version'); 