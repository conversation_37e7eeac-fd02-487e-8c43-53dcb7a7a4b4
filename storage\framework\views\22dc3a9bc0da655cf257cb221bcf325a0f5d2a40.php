<?php if(env('LOGIN_HELP') == 'active'): ?>
    <div class="alert alert-danger text-center mb-0" role="alert">
        This page only for addon
        <button type="button" id="topBannerClose" class="close float-end" data-dismiss="alert" aria-label="Close"><span
                aria-hidden="true">&times;</span></button>
    </div>
<?php endif; ?>
<section class="landing-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-2 col-6">
                <div class="max-w-174">
                    <a href="<?php echo e(route('frontend')); ?>">
                        <img src="<?php echo e(getSettingImage('app_logo')); ?>" alt="<?php echo e(getOption('app_name')); ?>"/>
                    </a>
                </div>
            </div>
            <div class="col-lg-7 col-xl-8 col-6">
                <nav class="navbar navbar-expand-lg p-0">
                    <button class="navbar-toggler landing-menu-navbar-toggler ms-auto" type="button"
                            data-bs-toggle="offcanvas" data-bs-target="#offcanvasNavbar" aria-controls="offcanvasNavbar"
                            aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="navbar-collapse landing-menu-navbar-collapse offcanvas offcanvas-start" tabindex="-1"
                         id="offcanvasNavbar" aria-labelledby="offcanvasNavbarLabel">
                        <button type="button"
                                class="d-lg-none w-30 h-30 p-0 rounded-circle bg-white border-0 position-absolute top-10 right-10"
                                data-bs-dismiss="offcanvas" aria-label="Close"><i class="fa-solid fa-times"></i>
                        </button>
                        <ul class="navbar-nav landing-menu-navbar-nav justify-content-lg-center flex-wrap w-100">
                            <li class="nav-item"><a class="nav-link" href="#features"><?php echo e(__('Features')); ?></a></li>
                            <li class="nav-item"><a class="nav-link" href="#price"><?php echo e(__('Pricing')); ?></a></li>
                            <li class="nav-item"><a class="nav-link" href="#faq"><?php echo e(__('FAQ\'s')); ?></a></li>
                            <li class="nav-item"><a class="nav-link" href="#integration"><?php echo e(__('Integrations')); ?></a>
                            </li>
                            <li class="nav-item d-lg-none">
                                <?php if(auth()->check()): ?>
                                    <?php if(auth()->user()->role == USER_ROLE_ADMIN): ?>
                                        <a href="<?php echo e(route('admin.dashboard')); ?>"
                                           class="nav-link"><?php echo e(__('Dashboard')); ?></a>
                                    <?php elseif(auth()->user()->role == USER_ROLE_USER): ?>
                                        <a href="<?php echo e(route('user.dashboard')); ?>"
                                           class="nav-link"><?php echo e(__('Dashboard')); ?></a>
                                    <?php else: ?>
                                        <a href="<?php echo e(route('affiliate.dashboard')); ?>"
                                           class="nav-link"><?php echo e(__('Dashboard')); ?></a>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <a href="<?php echo e(route('login')); ?>" class="nav-link"><?php echo e(__('Login')); ?></a>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                </nav>
            </div>
            <div class="col-lg-3 col-xl-2 d-none d-lg-block">
                <?php if(auth()->check()): ?>
                    <?php if(auth()->user()->role == USER_ROLE_ADMIN): ?>
                        <a href="<?php echo e(route('admin.dashboard')); ?>"
                           class="d-flex justify-content-center header-right-btn align-items-center py-17 px-10 bd-ra-12 bg-main-color fs-18 fw-600 lh-22 text-white"><?php echo e(__('Dashboard')); ?></a>
                    <?php elseif(auth()->user()->role == USER_ROLE_USER): ?>
                        <a href="<?php echo e(route('user.dashboard')); ?>"
                           class="d-flex justify-content-center header-right-btn align-items-center py-17 px-10 bd-ra-12 bg-main-color fs-18 fw-600 lh-22 text-white"><?php echo e(__('Dashboard')); ?></a>
                    <?php else: ?>
                        <a href="<?php echo e(route('affiliate.dashboard')); ?>"
                           class="d-flex justify-content-center header-right-btn align-items-center py-17 px-10 bd-ra-12 bg-main-color fs-18 fw-600 lh-22 text-white"><?php echo e(__('Dashboard')); ?></a>
                    <?php endif; ?>
                <?php else: ?>
                    <a href="<?php echo e(route('login')); ?>"
                       class="d-flex justify-content-center header-right-btn align-items-center py-17 px-10 bd-ra-12 bg-main-color fs-18 fw-600 lh-22 text-white"><?php echo e(__('Login')); ?></a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
<!-- Start banner -->
<div class="landing-hero-banner ld-container-1320"
     data-background="<?php echo e(asset(getFileUrl($section['hero_banner']?->banner_image))); ?>">
    <!-- Banner -->
    <div class="container">
        <?php if(isset($section['hero_banner']) && $section['hero_banner']->status == STATUS_ACTIVE): ?>
            <?php if(getOption('registration_status', 0) == ACTIVE): ?>
                <div class="landing-hero-banner-content pb-68">
                    <h4 class="title"><?php echo e(__($section['hero_banner']->title)); ?></h4>
                    <p class="info"><?php echo e(__($section['hero_banner']->description)); ?></p>
                    <a href="<?php echo e(route('user.register.form')); ?>" class="link"><?php echo e(__('Request A Demo')); ?></a>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
    <div class="landing-banner-img-one"><img src="<?php echo e(asset(getFileUrl($section['hero_banner']?->image))); ?>"
                                             alt="<?php echo e(getOption('app_name')); ?>"/></div>
</div>
<?php /**PATH C:\xampp\htdocs\zaisub\resources\views/saas/frontend/layouts/navbar.blade.php ENDPATH**/ ?>