# API REST - Documentation Complète

## Vue d'ensemble

Cette API REST permet aux plateformes SaaS d'intégrer notre système de gestion de commandes et d'utilisateurs. L'API utilise un système d'authentification par clé API sécurisé.

## Fonctionnalités principales

### 🔐 Authentification par clé API
- Génération automatique de clés API sécurisées
- Système de permissions granulaire (read, write, delete, admin)
- Expiration automatique des clés
- Suivi de l'utilisation des clés

### 👤 Gestion des utilisateurs
- Récupération du profil utilisateur
- Mise à jour des informations personnelles
- Changement de mot de passe sécurisé

### 📦 Gestion des commandes
- Création de nouvelles commandes
- Consultation de l'historique des commandes
- Annulation de commandes
- Statistiques détaillées

### 🔑 Gestion des clés API
- Création de nouvelles clés API
- Régénération de clés existantes
- Gestion des permissions
- Activation/désactivation des clés

## Installation et configuration

### 1. Migration de la base de données

```bash
php artisan migrate
```

### 2. Seeding des données de test (optionnel)

```bash
php artisan db:seed --class=ApiKeySeeder
```

### 3. Configuration des routes

Les routes API sont automatiquement configurées dans `routes/api.php`.

## Utilisation

### Authentification

Toutes les requêtes API (sauf la documentation) nécessitent une clé API valide.

#### Méthode 1 : En-tête X-API-Key (Recommandé)
```bash
curl -H "X-API-Key: sk_your_api_key_here" \
     https://your-domain.com/api/user/profile
```

#### Méthode 2 : En-tête Authorization
```bash
curl -H "Authorization: Bearer sk_your_api_key_here" \
     https://your-domain.com/api/user/profile
```

### Exemples d'utilisation

#### 1. Obtenir le profil utilisateur
```bash
curl -H "X-API-Key: sk_your_api_key_here" \
     https://your-domain.com/api/user/profile
```

#### 2. Créer une nouvelle commande
```bash
curl -X POST \
     -H "X-API-Key: sk_your_api_key_here" \
     -H "Content-Type: application/json" \
     -d '{
       "product_id": 1,
       "quantity": 2,
       "gateway_id": 1
     }' \
     https://your-domain.com/api/orders
```

#### 3. Lister les commandes
```bash
curl -H "X-API-Key: sk_your_api_key_here" \
     https://your-domain.com/api/orders?page=1&per_page=20
```

#### 4. Créer une nouvelle clé API
```bash
curl -X POST \
     -H "X-API-Key: sk_your_api_key_here" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Clé pour application mobile",
       "permissions": ["read", "write"],
       "expires_at": "2024-12-31T23:59:59Z"
     }' \
     https://your-domain.com/api/api-keys
```

## Endpoints disponibles

### Authentification
- `GET /api/test` - Test de l'API
- `GET /api/documentation` - Documentation complète

### Gestion des clés API
- `GET /api/api-keys` - Lister les clés API
- `POST /api/api-keys` - Créer une nouvelle clé
- `GET /api/api-keys/{id}` - Obtenir une clé spécifique
- `PUT /api/api-keys/{id}` - Mettre à jour une clé
- `DELETE /api/api-keys/{id}` - Supprimer une clé
- `POST /api/api-keys/{id}/regenerate` - Régénérer une clé

### Utilisateurs
- `GET /api/user/profile` - Obtenir le profil
- `PUT /api/user/profile` - Mettre à jour le profil
- `PUT /api/user/password` - Changer le mot de passe

### Commandes
- `GET /api/orders` - Lister les commandes
- `POST /api/orders` - Créer une commande
- `GET /api/orders/{id}` - Obtenir une commande
- `POST /api/orders/{id}/cancel` - Annuler une commande
- `GET /api/orders/stats` - Statistiques des commandes

## Codes de réponse

| Code | Description |
|------|-------------|
| 200 | Succès |
| 201 | Créé avec succès |
| 400 | Requête incorrecte |
| 401 | Non autorisé (clé API invalide) |
| 403 | Interdit (permissions insuffisantes) |
| 404 | Ressource non trouvée |
| 422 | Erreurs de validation |
| 429 | Trop de requêtes |
| 500 | Erreur serveur |

## Limitation de débit

L'API applique des limites de débit pour assurer la stabilité :
- 60 requêtes par minute
- 1000 requêtes par heure
- 10000 requêtes par jour

## Sécurité

### Bonnes pratiques
1. **Gardez vos clés API secrètes** - Ne les partagez jamais publiquement
2. **Utilisez HTTPS** - Toutes les requêtes doivent être chiffrées
3. **Régénérez régulièrement** - Changez vos clés périodiquement
4. **Limitez les permissions** - N'accordez que les permissions nécessaires
5. **Surveillez l'utilisation** - Vérifiez régulièrement l'activité de vos clés

### Permissions disponibles
- `read` - Lecture seule
- `write` - Lecture et écriture
- `delete` - Lecture, écriture et suppression
- `admin` - Toutes les permissions administratives
- `*` - Toutes les permissions

## Administration

### Interface d'administration

Accédez à l'interface d'administration pour gérer les clés API :
```
/admin/api-keys
```

### Fonctionnalités d'administration
- Création de clés API pour n'importe quel utilisateur
- Visualisation de toutes les clés API
- Modification des permissions
- Régénération de clés
- Activation/désactivation de clés
- Suppression de clés

## Support et contact

Pour toute question ou problème avec l'API :
- Consultez la documentation complète : `/api/documentation`
- Vérifiez les logs d'erreur
- Contactez l'équipe de support

## Changelog

### Version 1.0.0
- ✅ Authentification par clé API
- ✅ Gestion des utilisateurs
- ✅ Gestion des commandes
- ✅ Gestion des clés API
- ✅ Documentation complète
- ✅ Interface d'administration
- ✅ Système de permissions
- ✅ Limitation de débit
- ✅ Codes d'erreur standardisés 