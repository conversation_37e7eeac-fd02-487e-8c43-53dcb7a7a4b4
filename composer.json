{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "authorizenet/authorizenet": "^2.0", "ashallendesign/short-url": "^7.8", "bacon/bacon-qr-code": "^2.0", "barryvdh/laravel-dompdf": "^2.0", "doctrine/dbal": "^3.5", "guzzlehttp/guzzle": "^7.2", "iyzico/iyzipay-php": "^2.0", "paytm/paytmchecksum": "^1.1", "vrajroham/laravel-bitpay": "^6.0", "yansongda/pay": "^3.7", "jenssegers/agent": "^2.6", "josiasmontag/laravel-recaptchav3": "^1.0", "laravel/framework": "^9.19", "laravel/sanctum": "^3.0", "laravel/socialite": "^5.6", "laravel/tinker": "^2.7", "laravel/ui": "^4.1", "league/flysystem-aws-s3-v3": "^3.16", "mercadopago/dx-php": "^2.5", "milon/barcode": "^10.0", "mollie/laravel-mollie": "2.19", "omnipay/paypal": "^3.0", "srmklive/paypal": "^3.0", "php-http/guzzle7-adapter": "^1.0", "php-http/message-factory": "^1.1", "pragmarx/google2fa-laravel": "^2.1", "pragmarx/google2fa-qrcode": "^3.0", "rappasoft/laravel-authentication-log": "^2.0", "razorpay/razorpay": "^2.8", "spatie/laravel-backup": "^8.1", "spatie/laravel-cookie-consent": "^3.2", "spatie/laravel-permission": "^5.7", "stripe/stripe-php": "^9.6", "torann/geoip": "^3.0", "twilio/sdk": "^7.3", "yajra/laravel-datatables-oracle": "^10.2", "zainiklab/zai-installer": "^1.8"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.9", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/Helper.php", "app/Helpers/Constant.php", "app/Helpers/CoreArray.php", "app/Helpers/LangHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}