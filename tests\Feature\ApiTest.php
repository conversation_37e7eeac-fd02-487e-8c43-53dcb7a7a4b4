<?php

namespace Tests\Feature;

use App\Models\ApiKey;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ApiTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $apiKey;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer un utilisateur de test
        $this->user = User::factory()->create([
            'role' => 'user',
            'status' => 1
        ]);
        
        // Créer une clé API de test
        $this->apiKey = ApiKey::create([
            'user_id' => $this->user->id,
            'name' => 'Test API Key',
            'permissions' => ['read', 'write'],
            'is_active' => true
        ]);
    }

    /** @test */
    public function test_api_requires_authentication()
    {
        $response = $this->getJson('/api/user/profile');
        
        $response->assertStatus(401)
                ->assertJson([
                    'error' => 'Clé API manquante'
                ]);
    }

    /** @test */
    public function test_api_accepts_valid_api_key()
    {
        $response = $this->withHeaders([
            'X-API-Key' => $this->apiKey->key
        ])->getJson('/api/user/profile');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'name',
                        'email',
                        'role'
                    ]
                ]);
    }

    /** @test */
    public function test_api_accepts_bearer_token()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey->key
        ])->getJson('/api/user/profile');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function test_api_rejects_invalid_api_key()
    {
        $response = $this->withHeaders([
            'X-API-Key' => 'invalid_key'
        ])->getJson('/api/user/profile');
        
        $response->assertStatus(401)
                ->assertJson([
                    'error' => 'Clé API invalide'
                ]);
    }

    /** @test */
    public function test_api_rejects_expired_api_key()
    {
        $expiredKey = ApiKey::create([
            'user_id' => $this->user->id,
            'name' => 'Expired Key',
            'permissions' => ['read'],
            'expires_at' => now()->subDay(),
            'is_active' => true
        ]);
        
        $response = $this->withHeaders([
            'X-API-Key' => $expiredKey->key
        ])->getJson('/api/user/profile');
        
        $response->assertStatus(401)
                ->assertJson([
                    'error' => 'Clé API inactive ou expirée'
                ]);
    }

    /** @test */
    public function test_api_rejects_inactive_api_key()
    {
        $inactiveKey = ApiKey::create([
            'user_id' => $this->user->id,
            'name' => 'Inactive Key',
            'permissions' => ['read'],
            'is_active' => false
        ]);
        
        $response = $this->withHeaders([
            'X-API-Key' => $inactiveKey->key
        ])->getJson('/api/user/profile');
        
        $response->assertStatus(401)
                ->assertJson([
                    'error' => 'Clé API inactive ou expirée'
                ]);
    }

    /** @test */
    public function test_user_can_get_profile()
    {
        $response = $this->withHeaders([
            'X-API-Key' => $this->apiKey->key
        ])->getJson('/api/user/profile');
        
        $response->assertStatus(200)
                ->assertJson([
                    'data' => [
                        'id' => $this->user->id,
                        'name' => $this->user->name,
                        'email' => $this->user->email
                    ]
                ]);
    }

    /** @test */
    public function test_user_can_update_profile()
    {
        $response = $this->withHeaders([
            'X-API-Key' => $this->apiKey->key
        ])->putJson('/api/user/profile', [
            'name' => 'Updated Name',
            'mobile' => '+33123456789'
        ]);
        
        $response->assertStatus(200)
                ->assertJson([
                    'message' => 'Profil mis à jour avec succès'
                ]);
        
        $this->assertDatabaseHas('users', [
            'id' => $this->user->id,
            'name' => 'Updated Name',
            'mobile' => '+33123456789'
        ]);
    }

    /** @test */
    public function test_user_can_list_api_keys()
    {
        $response = $this->withHeaders([
            'X-API-Key' => $this->apiKey->key
        ])->getJson('/api/api-keys');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'key',
                            'permissions',
                            'is_active'
                        ]
                    ]
                ]);
    }

    /** @test */
    public function test_user_can_create_api_key()
    {
        $response = $this->withHeaders([
            'X-API-Key' => $this->apiKey->key
        ])->postJson('/api/api-keys', [
            'name' => 'New API Key',
            'permissions' => ['read']
        ]);
        
        $response->assertStatus(201)
                ->assertJson([
                    'message' => 'Clé API créée avec succès'
                ])
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'name',
                        'key',
                        'permissions'
                    ]
                ]);
    }

    /** @test */
    public function test_api_test_endpoint()
    {
        $response = $this->getJson('/api/test');
        
        $response->assertStatus(200)
                ->assertJson([
                    'message' => 'API fonctionnelle',
                    'version' => '1.0.0'
                ])
                ->assertJsonStructure([
                    'timestamp'
                ]);
    }

    /** @test */
    public function test_documentation_endpoint()
    {
        $response = $this->get('/api/documentation');
        
        $response->assertStatus(200);
    }
} 