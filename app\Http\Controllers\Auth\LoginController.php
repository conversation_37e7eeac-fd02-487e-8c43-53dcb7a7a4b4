<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\AffiliateRegisterRequest;
use App\Http\Requests\LoginRequest;
use App\Models\User;
use Illuminate\Http\Request;
use App\Providers\RouteServiceProvider;
use Carbon\Carbon;
use Exception;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * Validate the user login request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateLogin(Request $request)
    {
        if (!empty(getOption('google_recaptcha_status')) && getOption('google_recaptcha_status') == 1) {
            $rules['g-recaptcha-response'] = ['required', 'recaptchav3:register,0.5'];
        } else {
            $rules = [
                $this->username() => 'required|string',
                'password' => 'required|string',
            ];
        }
        $request->validate($rules);
    }

    public function login(LoginRequest $request)
    {
        Session::put('2fa_status', false);
        $field = 'email';
        $request->merge([$field => $request->input('email')]);
        $credentials = $request->only($field, 'password');
        $remember = request('remember');
        if (!Auth::attempt($credentials, $remember)) {
            return redirect("login")->withInput()->with('error',  __('Email or password is incorrect'));
        }

        $user = User::where('email', $request->email)->first();
        if ($user->email_verification_status == STATUS_ACTIVE) {
            if ($user->status == STATUS_SUSPENDED) {
                Auth::logout();
                return redirect("login")->withInput()->with('error', __('Your account is suspended Please contact our support center'));
            } elseif ($user->deleted_at != null) {
                Auth::logout();
                return redirect("login")->withInput()->with('error', __('Your account has been deleted'));
            }
            if (isset($user) && ($user->status == STATUS_PENDING)) {
                Auth::logout();
                return redirect("login")->with('error', __('Your account is under approval. Please wait for approval'));
            } else if (isset($user) && ($user->status == STATUS_REJECT) || ($user->status == STATUS_DEACTIVATE)) {
                Auth::logout();
                return redirect("login")->withInput()->with('error', __('Your account is inactive. Please contact with admin'));
            } else {
                addUserActivityLog('Sign In', $user->id);
                return redirect('login')->with('error', __(SOMETHING_WENT_WRONG));
            }
        }elseif($user->role == USER_ROLE_CUSTOMER ){
            Auth::logout();
            return redirect('login')->with('error', __('Invalid user'));
        }

        return redirect('login');
    }

    public function affiliate_register_form($hash)
    {
        $user = User::where('uuid', $hash);
        $data['hash'] = $hash;
        return view('auth.affiliate', $data);
    }

    public function affiliate_register_store(AffiliateRegisterRequest $request)
    {
        DB::beginTransaction();
        try {
            $userExist = User::where('uuid', $request->hash)->first();
            if (is_null($userExist)) {
                throw new Exception(__(SOMETHING_WENT_WRONG));
            }
            $user = new User();
            $user->created_by = $userExist->id;
            $user->name = $request->name;
            $user->mobile = $request->mobile;
            $user->email = $request->email;
            $user->password = Hash::make($request->password);
            $user->role = USER_ROLE_AFFILIATE;
            $user->verify_token = str_replace('-', '', Str::uuid()->toString());
            $user->status = STATUS_PENDING;
            $user->affiliate_code = Str::random(32);
            $user->email_verified_at = Carbon::now()->format("Y-m-d H:i:s");
            $user->email_verification_status = STATUS_ACTIVE;
            $user->save();

            DB::commit();

            $credentials = ['email' => $request->email, 'password' => $request->password];
            if (Auth::attempt($credentials)) {
                return redirect()->back()->with('success', __(CREATED_SUCCESSFULLY));
            } else {
                return redirect()->back()->with('error', __(SOMETHING_WENT_WRONG));
            }
        } catch (Exception $e) {
            DB::rollBack();
            return back()->with('error', $e->getMessage());
        }
    }
}
