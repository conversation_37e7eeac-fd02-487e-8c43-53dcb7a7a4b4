<?php $__env->startPush('title'); ?>
    <?php echo e(__('Login')); ?>

<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="zMain-signLog overflow-hidden">
        <div class="zMain-signLog-wrap">
            <div class="pb-34">
                <a href="<?php echo e(route('frontend')); ?>" class="max-w-163"><img src="<?php echo e(getSettingImage('app_logo')); ?>"
                        alt="<?php echo e(getOption('app_name')); ?>" /></a>
            </div>
            <div class="pb-30">
                <h4 class="fs-32 fw-600 lh-40 text-textBlack pb-5"><?php echo e(__('Sign In')); ?></h4>
                <?php if(isAddonInstalled('SUBSAAS') > 0): ?>
                    <?php if(getOption('registration_status', 0) == ACTIVE): ?>
                        <h6 class="fs-14 fw-500 lh-24 text-para-text"><?php echo e(__('Don’t have an account')); ?>? <a
                                href="<?php echo e(route('user.register.form')); ?>" class="text-main-color"><?php echo e(__('Sign Up')); ?></a>
                        </h6>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            <form method="POST" action="<?php echo e(route('login')); ?>">
                <?php echo csrf_field(); ?>
                <div class="zForm-wrap">
                    <label for="eInputEmailAddress" class="zForm-label"><?php echo e(__('Email')); ?></label>
                    <input type="email" name="email" class="form-control zForm-control" id="eInputEmailAddress"
                        placeholder="<?php echo e(__('Enter email address')); ?>" />
                </div>
                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <span class="fs-12 text-danger"><?php echo e($message); ?></span>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <div class="zForm-wrap zForm-password mt-3">
                    <label for="signInPassword" class="zForm-label"><?php echo e(__('Password')); ?></label>
                    <input type="password" name="password" class="form-control zForm-control" id="signInPassword"
                        placeholder="<?php echo e(__('Enter password')); ?>" />
                    <span class="icon"><img src="<?php echo e(asset('user/images/icon/eye.svg')); ?>" alt="" /></span>
                </div>
                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <span class="fs-12 text-danger"><?php echo e($message); ?></span>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="zForm-wrap-checkbox">
                        <input class="form-check-input" type="checkbox" id="rememberMe" name="remember" value="1" />
                        <label class="form-check-label" for="rememberMe"><?php echo e(__('Remember me')); ?></label>
                    </div>
                    <a href="<?php echo e(route('password.request')); ?>"
                        class="fs-14 fw-500 lh-24 text-main-color"><?php echo e(__('Forgot Password?')); ?></a>
                </div>
                <?php if(getOption('google_recaptcha_status') == 1): ?>
                    <div class="g-recaptcha my-lg-4" id="feedback-recaptcha"
                         data-sitekey="<?php echo e(getOption('google_recaptcha_site_key')); ?>">
                    </div>
                <?php endif; ?>
                <button type="submit"
                    class="align-items-center bd-ra-10 bg-main-color border-0 d-flex fs-16 fw-600 justify-content-center lh-19 mb-13 mt-2 p-13 text-white w-100"><?php echo e(__('Sign In')); ?></button>
            </form>

            <!-- Another Sign In options -->
            <?php if(getOption('google_login_status') == 1 || getOption('facebook_login_status') == 1): ?>
                <h4 class="position-relative fs-14 fw-400 lh-24 text-para-text text-center mb-20 under-border-one"><span
                        class="bg-white position-relative px-10"><?php echo e(__('Or continue with')); ?></span></h4>

                <ul class="d-flex justify-content-center align-items-center g-10">
                    <?php if(getOption('facebook_login_status') == 1): ?>
                        <li>
                            <a href="<?php echo e(route('facebook-login')); ?>"
                                class="w-56 h-56 rounded-circle bd-one bd-c-stroke-color d-flex justify-content-center align-items-center bg-transparent">
                                <img src="<?php echo e(asset('user/images/icon/facebook.svg')); ?>" alt="<?php echo e(__('facebook')); ?>" />
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(getOption('google_login_status') == 1): ?>
                        <li>
                            <a href="<?php echo e(route('google-login')); ?>"
                                class="w-56 h-56 rounded-circle bd-one bd-c-stroke-color d-flex justify-content-center align-items-center bg-transparent">
                                <img src="<?php echo e(asset('user/images/icon/google.svg')); ?>" alt="<?php echo e(__('google')); ?>" />
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            <?php endif; ?>
            <?php if(env('LOGIN_HELP') == 'active'): ?>
                <div class="row">
                    <div class="col-md-12 mb-25">
                        <div class="table-responsive login-info-table mt-3">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <td colspan="2" id="adminCredentialShow" class="login-info">
                                            <b><?php echo e(__('Admin ')); ?>:</b> <?php echo e(__('<EMAIL>')); ?> | 123456
                                            <span class="badge bg-danger "><a href="<?php echo e(LINK_SAAS_ADDON); ?>" target="_blank" style="color: white">SAAS Addon</a></span>
                                            <p class="font-16 pt-2">
                                                <a  href="/" class="secondary-color font-medium">SAAS Landing Page</a>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" id="userCredentialShow" class="login-info">
                                            <b><?php echo e(__('User')); ?> :</b> <?php echo e(__('<EMAIL>')); ?> | 123456
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" id="affiliateCredentialShow" class="login-info">
                                            <b><?php echo e(__('Affiliator')); ?> :</b> <?php echo e(__('<EMAIL>')); ?> | 123456
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <script>
        "use strict"
        $('#adminCredentialShow').on('click', function() {
            $('#eInputEmailAddress').val('<EMAIL>');
            $('#signInPassword').val('123456');
        });
        $('#userCredentialShow').on('click', function() {
            $('#eInputEmailAddress').val('<EMAIL>');
            $('#signInPassword').val('123456');
        });
        $('#affiliateCredentialShow').on('click', function() {
            $('#eInputEmailAddress').val('<EMAIL>');
            $('#signInPassword').val('123456');
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('auth.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaisub\resources\views/auth/login.blade.php ENDPATH**/ ?>