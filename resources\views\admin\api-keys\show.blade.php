@extends('admin.layouts.master')
@section('title', 'Détails de la Clé API')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-flex align-items-center justify-content-between">
                <h4 class="mb-0">Détails de la Clé API</h4>
                <div class="page-title-right">
                    <a href="{{ route('admin.api-keys.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                    <a href="{{ route('admin.api-keys.edit', $apiKey->id) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Modifier
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="card-title">Informations de base</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>ID :</strong></td>
                                    <td>{{ $apiKey->id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Nom :</strong></td>
                                    <td>{{ $apiKey->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Clé API :</strong></td>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control" value="{{ $apiKey->key }}" readonly id="apiKey">
                                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard()">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                        <small class="text-muted">Cliquez sur le bouton copier pour copier la clé</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Statut :</strong></td>
                                    <td>
                                        @if($apiKey->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Créée le :</strong></td>
                                    <td>{{ $apiKey->created_at->format('d/m/Y H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Modifiée le :</strong></td>
                                    <td>{{ $apiKey->updated_at->format('d/m/Y H:i:s') }}</td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6">
                            <h5 class="card-title">Informations utilisateur</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Utilisateur :</strong></td>
                                    <td>{{ $apiKey->user->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email :</strong></td>
                                    <td>{{ $apiKey->user->email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Rôle :</strong></td>
                                    <td>{{ ucfirst($apiKey->user->role) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Statut utilisateur :</strong></td>
                                    <td>
                                        @if($apiKey->user->status == 1)
                                            <span class="badge bg-success">Actif</span>
                                        @else
                                            <span class="badge bg-danger">Inactif</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>

                            <h5 class="card-title mt-4">Permissions</h5>
                            @if($apiKey->permissions)
                                <div class="mb-3">
                                    @foreach($apiKey->permissions as $permission)
                                        <span class="badge bg-info me-1">{{ $permission }}</span>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-muted">Aucune permission définie</p>
                            @endif
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="card-title">Informations d'utilisation</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Dernière utilisation :</strong></td>
                                            <td>
                                                @if($apiKey->last_used_at)
                                                    {{ $apiKey->last_used_at->format('d/m/Y H:i:s') }}
                                                @else
                                                    <span class="text-muted">Jamais utilisée</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Expire le :</strong></td>
                                            <td>
                                                @if($apiKey->expires_at)
                                                    {{ $apiKey->expires_at->format('d/m/Y H:i:s') }}
                                                    @if($apiKey->isExpired())
                                                        <span class="badge bg-danger ms-1">Expirée</span>
                                                    @endif
                                                @else
                                                    <span class="text-muted">Jamais</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="card-title">Actions</h5>
                            <div class="btn-group" role="group">
                                <a href="{{ route('admin.api-keys.edit', $apiKey->id) }}" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> Modifier
                                </a>
                                <a href="{{ route('admin.api-keys.regenerate', $apiKey->id) }}" 
                                   class="btn btn-secondary"
                                   onclick="return confirm('Êtes-vous sûr de vouloir régénérer cette clé ?')">
                                    <i class="fas fa-sync"></i> Régénérer
                                </a>
                                <a href="{{ route('admin.api-keys.toggle', $apiKey->id) }}" 
                                   class="btn btn-{{ $apiKey->is_active ? 'warning' : 'success' }}">
                                    <i class="fas fa-{{ $apiKey->is_active ? 'pause' : 'play' }}"></i> 
                                    {{ $apiKey->is_active ? 'Désactiver' : 'Activer' }}
                                </a>
                                <a href="{{ route('admin.api-keys.delete', $apiKey->id) }}" 
                                   class="btn btn-danger"
                                   onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette clé ?')">
                                    <i class="fas fa-trash"></i> Supprimer
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function copyToClipboard() {
        const apiKey = document.getElementById('apiKey');
        apiKey.select();
        apiKey.setSelectionRange(0, 99999); // For mobile devices
        
        navigator.clipboard.writeText(apiKey.value).then(function() {
            // Show success message
            const button = event.target.closest('button');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-success');
            
            setTimeout(function() {
                button.innerHTML = originalHTML;
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-secondary');
            }, 2000);
        }).catch(function(err) {
            console.error('Erreur lors de la copie: ', err);
        });
    }

    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
</script>
@endpush 