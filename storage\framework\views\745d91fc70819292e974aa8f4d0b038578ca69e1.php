<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <?php if(isset($_SERVER['HTTPS'])): ?>
        <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" />
    <?php endif; ?>
    <meta name="title" content="<?php echo e(getOption('app_name')); ?>">
    <meta name="description" content="<?php echo e(getOption('meta_description')); ?>">
    <meta name="keywords" content="<?php echo e(getOption('meta_keyword')); ?>">
    <meta name="author" content="<?php echo e(getOption('meta_author')); ?>">

    <meta property="og:type" content="subscription">
    <meta property="og:title" content="<?php echo e(getOption('app_name')); ?>">
    <meta property="og:description" content="<?php echo e(getOption('meta_description')); ?>">
    <meta property="og:image" content="<?php echo e(getSettingImage('app_logo')); ?>">

    <meta name="twitter:card" content="<?php echo e(getOption('app_name')); ?>">
    <meta name="twitter:title" content="<?php echo e(getOption('app_name')); ?>">
    <meta name="twitter:description" content="<?php echo e(getOption('meta_description')); ?>">
    <meta name="twitter:image" content="<?php echo e(getSettingImage('app_logo')); ?>">

    <meta name="msapplication-TileImage" content="<?php echo e(getSettingImage('app_logo')); ?>">

    <meta name="msapplication-TileColor" content="#F8F8F8">
    <meta name="theme-color" content="#3686FC">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>" />
    <title><?php echo e(getOption('app_name') . ' - ' . @$pageTitle); ?></title>

    <?php echo $__env->make('user.layouts.style', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- FAVICONS -->
    <link rel="icon" href="<?php echo e(getSettingImage('app_fav_icon')); ?>" type="image/png" sizes="16x16">
    <link rel="shortcut icon" href="<?php echo e(getSettingImage('app_fav_icon')); ?>" type="image/x-icon">
    <link rel="shortcut icon" href="<?php echo e(getSettingImage('app_fav_icon')); ?>">

    <link rel="stylesheet" href="<?php echo e(asset('assets/scss/extra-style.css')); ?>" />
</head>

<body class="<?php echo e(selectedLanguage()->rtl == 1 ? 'direction-rtl' : 'direction-ltr'); ?> <?php echo e(!(getOption('app_color_design_type', DEFAULT_COLOR) == DEFAULT_COLOR) ? 'custom-color' : ''); ?>">
<?php echo $__env->make('saas.frontend.layouts.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->yieldContent('content'); ?>
    <?php echo $__env->make('saas.frontend.layouts.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('user.layouts.script', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('admin.setting.partials.dynamic-color', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\zaisub\resources\views/saas/frontend/layouts/app.blade.php ENDPATH**/ ?>