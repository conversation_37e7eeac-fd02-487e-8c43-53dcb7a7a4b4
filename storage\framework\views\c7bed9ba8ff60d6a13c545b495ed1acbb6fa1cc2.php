<!DOCTYPE html>
<html class="no-js" lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title><?php echo $__env->yieldPushContent('title' ?? ''); ?> - <?php echo e(getOption('app_name')); ?></title>
    <?php if (! empty(trim($__env->yieldContent('meta')))): ?>
        <?php echo $__env->yieldPushContent('meta'); ?>
    <?php else: ?>
        <?php
            $metaData = getMeta('home');
        ?>

        <!-- Open Graph meta tags for social sharing -->
        <meta property="og:type" content="<?php echo e(__('zaisub')); ?>">
        <meta property="og:title" content="<?php echo e($metaData['meta_title'] ?? getOption('app_name')); ?>">
        <meta property="og:description" content="<?php echo e($metaData['meta_description'] ?? getOption('app_name')); ?>">
        <meta property="og:image" content="<?php echo e($metaData['og_image'] ?? getSettingImage('app_logo')); ?>">
        <meta property="og:url" content="<?php echo e(url()->current()); ?>">
        <meta property="og:site_name" content="<?php echo e(getOption('app_name')); ?>">

        <!-- Twitter Card meta tags for Twitter sharing -->
        <meta name="twitter:card" content="<?php echo e(__('zaisub')); ?>">
        <meta name="twitter:title" content="<?php echo e($metaData['meta_title'] ?? getOption('app_name')); ?>">
        <meta name="twitter:description" content="<?php echo e($metaData['meta_description'] ?? getOption('app_name')); ?>">
        <meta name="twitter:image" content="<?php echo e($metaData['og_image'] ?? getSettingImage('app_logo')); ?>">

        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>" />
    <?php endif; ?>
    <!-- Place favicon.ico in the root directory -->
    <link rel="shortcut icon" href="<?php echo e(asset('user/images/favicon.png')); ?>" type="image/x-icon" />
    <!-- css file  -->
    <link rel="stylesheet" href="<?php echo e(asset('user/css/bootstrap.min.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('user/css/plugins.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('user/css/dataTables.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('user/css/dataTables.responsive.min.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('user/css/summernote/summernote-lite.min.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('user/scss/style.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/scss/extra-style.css')); ?>" />

</head>

<body class="<?php echo e(selectedLanguage()->rtl == 1 ? 'direction-rtl' : 'direction-ltr'); ?> <?php echo e(!(getOption('app_color_design_type', DEFAULT_COLOR) == DEFAULT_COLOR) ? 'custom-color' : ''); ?>">
    <!-- Main Content -->
    <?php echo $__env->yieldContent('content'); ?>
    <?php echo $__env->make('admin.setting.partials.dynamic-color', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!-- js file  -->
    <script src="<?php echo e(asset('user/js/jquery-3.7.0.min.js')); ?>"></script>
    <script src="<?php echo e(asset('user/js/bootstrap.min.js')); ?>"></script>
    <script src="<?php echo e(asset('user/js/plugins.js')); ?>"></script>
    <script src="<?php echo e(asset('user/js/dataTables.js')); ?>"></script>
    <script src="<?php echo e(asset('user/js/dataTables.responsive.min.js')); ?>"></script>
    <script src="<?php echo e(asset('user/css/summernote/summernote-lite.min.js')); ?>"></script>
    <script src="<?php echo e(asset('user/js/main.js')); ?>"></script>
    <?php echo $__env->yieldPushContent('script'); ?>
    <script>
        <?php if(Session::has('success')): ?>
            toastr.success("<?php echo e(session('success')); ?>");
        <?php endif; ?>
        <?php if(Session::has('error')): ?>
            toastr.error("<?php echo e(session('error')); ?>");
        <?php endif; ?>
        <?php if(Session::has('info')): ?>
            toastr.info("<?php echo e(session('info')); ?>");
        <?php endif; ?>
        <?php if(Session::has('warning')): ?>
            toastr.warning("<?php echo e(session('warning')); ?>");
        <?php endif; ?>

        <?php if(@$errors->any()): ?>
            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                toastr.error("<?php echo e($error); ?>");
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
    </script>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\zaisub\resources\views/auth/layouts/app.blade.php ENDPATH**/ ?>