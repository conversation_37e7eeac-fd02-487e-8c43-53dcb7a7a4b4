<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    /**
     * Obtenir le profil de l'utilisateur connecté
     */
    public function profile(Request $request)
    {
        $user = $request->api_user;
        
        return response()->json([
            'data' => [
                'id' => $user->id,
                'uuid' => $user->uuid,
                'name' => $user->name,
                'nick_name' => $user->nick_name,
                'email' => $user->email,
                'mobile' => $user->mobile,
                'country' => $user->country,
                'city' => $user->city,
                'zip_code' => $user->zip_code,
                'address' => $user->address,
                'currency' => $user->currency,
                'company_name' => $user->company_name,
                'company_country' => $user->company_country,
                'company_city' => $user->company_city,
                'company_designation' => $user->company_designation,
                'company_zip_code' => $user->company_zip_code,
                'company_address' => $user->company_address,
                'email_verified_at' => $user->email_verified_at,
                'image' => $user->image,
                'role' => $user->role,
                'status' => $user->status,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at
            ]
        ]);
    }

    /**
     * Mettre à jour le profil de l'utilisateur
     */
    public function updateProfile(Request $request)
    {
        $user = $request->api_user;

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'nick_name' => 'sometimes|string|max:255',
            'mobile' => 'sometimes|string|max:20',
            'country' => 'sometimes|string|max:100',
            'city' => 'sometimes|string|max:100',
            'zip_code' => 'sometimes|string|max:20',
            'address' => 'sometimes|string',
            'currency' => 'sometimes|string|max:10',
            'company_name' => 'sometimes|string|max:255',
            'company_country' => 'sometimes|string|max:100',
            'company_city' => 'sometimes|string|max:100',
            'company_designation' => 'sometimes|string|max:255',
            'company_zip_code' => 'sometimes|string|max:20',
            'company_address' => 'sometimes|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Données invalides',
                'errors' => $validator->errors()
            ], Response::HTTP_BAD_REQUEST);
        }

        $user->update($request->only([
            'name', 'nick_name', 'mobile', 'country', 'city', 'zip_code', 'address',
            'currency', 'company_name', 'company_country', 'company_city',
            'company_designation', 'company_zip_code', 'company_address'
        ]));

        return response()->json([
            'message' => 'Profil mis à jour avec succès',
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'updated_at' => $user->updated_at
            ]
        ]);
    }

    /**
     * Changer le mot de passe
     */
    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8|confirmed',
            'new_password_confirmation' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Données invalides',
                'errors' => $validator->errors()
            ], Response::HTTP_BAD_REQUEST);
        }

        $user = $request->api_user;

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'error' => 'Mot de passe actuel incorrect'
            ], Response::HTTP_BAD_REQUEST);
        }

        $user->update([
            'password' => Hash::make($request->new_password)
        ]);

        return response()->json([
            'message' => 'Mot de passe modifié avec succès'
        ]);
    }
} 