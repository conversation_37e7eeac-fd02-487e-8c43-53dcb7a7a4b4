<?php

namespace App\Http\Middleware;

use App\Models\ApiKey;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ApiKeyMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, $permission = null)
    {
        $apiKey = $request->header('X-API-Key') ?? $request->header('Authorization');
        
        if (!$apiKey) {
            return response()->json([
                'error' => 'Clé API manquante',
                'message' => 'Veuillez fournir une clé API valide dans l\'en-tête X-API-Key ou Authorization'
            ], Response::HTTP_UNAUTHORIZED);
        }

        // Supprimer le préfixe "Bearer " si présent
        $apiKey = str_replace('Bearer ', '', $apiKey);

        $key = ApiKey::where('key', $apiKey)->first();

        if (!$key) {
            return response()->json([
                'error' => 'Clé API invalide',
                'message' => 'La clé API fournie n\'est pas valide'
            ], Response::HTTP_UNAUTHORIZED);
        }

        if (!$key->canUse()) {
            return response()->json([
                'error' => 'Clé API inactive ou expirée',
                'message' => 'Cette clé API est inactive ou a expiré'
            ], Response::HTTP_UNAUTHORIZED);
        }

        // Vérifier les permissions si spécifiées
        if ($permission && !$key->hasPermission($permission)) {
            return response()->json([
                'error' => 'Permission insuffisante',
                'message' => 'Cette clé API n\'a pas la permission requise: ' . $permission
            ], Response::HTTP_FORBIDDEN);
        }

        // Mettre à jour la dernière utilisation
        $key->update(['last_used_at' => now()]);

        // Ajouter l'utilisateur et la clé API à la requête
        $request->merge(['api_user' => $key->user]);
        $request->merge(['api_key' => $key]);

        return $next($request);
    }
} 