-- MySQL dump 10.13  Distrib 8.0.33, for Linux (x86_64)
--
-- Host: 127.0.0.1    Database: zaisub
-- ------------------------------------------------------
-- Server version   8.0.33-0ubuntu0.22.10.2

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `affiliate_configs`
--

DROP TABLE IF EXISTS `affiliate_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `affiliate_configs` (
                                     `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                     `user_id` bigint unsigned DEFAULT NULL,
                                     `title` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                                     `products` text COLLATE utf8mb4_unicode_ci,
                                     `plans` text COLLATE utf8mb4_unicode_ci,
                                     `affiliates` text COLLATE utf8mb4_unicode_ci,
                                     `commission_type` tinyint NOT NULL DEFAULT '1',
                                     `commission_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
                                     `recurring_commission_type` tinyint NOT NULL DEFAULT '1',
                                     `recurring_commission_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
                                     `created_at` timestamp NULL DEFAULT NULL,
                                     `updated_at` timestamp NULL DEFAULT NULL,
                                     `deleted_at` timestamp NULL DEFAULT NULL,
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `affiliate_configs`
--

/*!40000 ALTER TABLE `affiliate_configs` DISABLE KEYS */;
/*!40000 ALTER TABLE `affiliate_configs` ENABLE KEYS */;

--
-- Table structure for table `affiliate_histories`
--

DROP TABLE IF EXISTS `affiliate_histories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `affiliate_histories` (
                                       `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                       `user_id` bigint unsigned NOT NULL,
                                       `product_id` int DEFAULT NULL,
                                       `plan_id` int DEFAULT NULL,
                                       `amount` decimal(12,2) NOT NULL DEFAULT '0.00',
                                       `created_at` timestamp NULL DEFAULT NULL,
                                       `updated_at` timestamp NULL DEFAULT NULL,
                                       `deleted_at` timestamp NULL DEFAULT NULL,
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `affiliate_histories`
--

/*!40000 ALTER TABLE `affiliate_histories` DISABLE KEYS */;
/*!40000 ALTER TABLE `affiliate_histories` ENABLE KEYS */;

--
-- Table structure for table `authentication_log`
--

DROP TABLE IF EXISTS `authentication_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `authentication_log` (
                                      `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                      `authenticatable_type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                                      `authenticatable_id` bigint unsigned NOT NULL,
                                      `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                      `user_agent` text COLLATE utf8mb4_unicode_ci,
                                      `login_at` timestamp NULL DEFAULT NULL,
                                      `login_successful` tinyint(1) NOT NULL DEFAULT '0',
                                      `logout_at` timestamp NULL DEFAULT NULL,
                                      `cleared_by_user` tinyint(1) NOT NULL DEFAULT '0',
                                      `location` text COLLATE utf8mb4_unicode_ci,
                                      PRIMARY KEY (`id`),
                                      KEY `authentication_log_authenticatable_type_authenticatable_id_index` (`authenticatable_type`,`authenticatable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `authentication_log`
--

/*!40000 ALTER TABLE `authentication_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `authentication_log` ENABLE KEYS */;

--
-- Table structure for table `banks`
--

DROP TABLE IF EXISTS `banks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `banks` (
                         `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                         `user_id` bigint unsigned DEFAULT NULL,
                         `gateway_id` bigint unsigned NOT NULL,
                         `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                         `details` text COLLATE utf8mb4_unicode_ci NOT NULL,
                         `status` tinyint NOT NULL DEFAULT '0',
                         `created_at` timestamp NULL DEFAULT NULL,
                         `updated_at` timestamp NULL DEFAULT NULL,
                         `deleted_at` timestamp NULL DEFAULT NULL,
                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `banks`
--

/*!40000 ALTER TABLE `banks` DISABLE KEYS */;
/*!40000 ALTER TABLE `banks` ENABLE KEYS */;

--
-- Table structure for table `beneficiaries`
--

DROP TABLE IF EXISTS `beneficiaries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `beneficiaries` (
                                 `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                 `user_id` bigint unsigned NOT NULL,
                                 `beneficiary_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                                 `type` tinyint NOT NULL,
                                 `card_number` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 `card_holder_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 `expire_month` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 `expire_year` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 `bank_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 `bank_account_number` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 `bank_account_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 `bank_routing_number` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 `paypal_email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 `status` tinyint NOT NULL DEFAULT '1',
                                 `deleted_at` timestamp NULL DEFAULT NULL,
                                 `created_at` timestamp NULL DEFAULT NULL,
                                 `updated_at` timestamp NULL DEFAULT NULL,
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `beneficiaries`
--

/*!40000 ALTER TABLE `beneficiaries` DISABLE KEYS */;
/*!40000 ALTER TABLE `beneficiaries` ENABLE KEYS */;

--
-- Table structure for table `best_features_settings`
--

DROP TABLE IF EXISTS `best_features_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `best_features_settings` (
                                          `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                          `name` text COLLATE utf8mb4_unicode_ci NOT NULL,
                                          `title` text COLLATE utf8mb4_unicode_ci NOT NULL,
                                          `description` longtext COLLATE utf8mb4_unicode_ci,
                                          `image` int DEFAULT NULL,
                                          `status` tinyint NOT NULL DEFAULT '1',
                                          `created_at` timestamp NULL DEFAULT NULL,
                                          `updated_at` timestamp NULL DEFAULT NULL,
                                          `deleted_at` timestamp NULL DEFAULT NULL,
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `best_features_settings`
--

/*!40000 ALTER TABLE `best_features_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `best_features_settings` ENABLE KEYS */;

--
-- Table structure for table `checkout_page_settings`
--

DROP TABLE IF EXISTS `checkout_page_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `checkout_page_settings` (
                                          `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                          `user_id` bigint unsigned DEFAULT NULL,
                                          `image` int DEFAULT NULL,
                                          `title` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `text_size` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `text_color` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `basic_info` longtext COLLATE utf8mb4_unicode_ci,
                                          `basic_first_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `basic_last_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `basic_phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `basic_email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `basic_company` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `billing_info` longtext COLLATE utf8mb4_unicode_ci,
                                          `billing_first_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `billing_last_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `billing_email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `billing_phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `billing_zip_code` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `billing_address` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `billing_city` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `billing_state` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `billing_country` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `shipping_info` longtext COLLATE utf8mb4_unicode_ci,
                                          `shipping_first_name` longtext COLLATE utf8mb4_unicode_ci,
                                          `shipping_last_name` longtext COLLATE utf8mb4_unicode_ci,
                                          `shipping_email` longtext COLLATE utf8mb4_unicode_ci,
                                          `shipping_phone` longtext COLLATE utf8mb4_unicode_ci,
                                          `shipping_zip_code` longtext COLLATE utf8mb4_unicode_ci,
                                          `shipping_address` longtext COLLATE utf8mb4_unicode_ci,
                                          `shipping_city` longtext COLLATE utf8mb4_unicode_ci,
                                          `shipping_state` longtext COLLATE utf8mb4_unicode_ci,
                                          `shipping_country` longtext COLLATE utf8mb4_unicode_ci,
                                          `shipping_method` tinyint DEFAULT NULL,
                                          `payment` longtext COLLATE utf8mb4_unicode_ci,
                                          `status` tinyint NOT NULL DEFAULT '2',
                                          `created_at` timestamp NULL DEFAULT NULL,
                                          `updated_at` timestamp NULL DEFAULT NULL,
                                          `deleted_at` timestamp NULL DEFAULT NULL,
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `checkout_page_settings`
--

/*!40000 ALTER TABLE `checkout_page_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `checkout_page_settings` ENABLE KEYS */;

--
-- Table structure for table `contact_messages`
--

DROP TABLE IF EXISTS `contact_messages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `contact_messages` (
                                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                    `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                                    `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                                    `subject` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                                    `phone` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                                    `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
                                    `created_at` timestamp NULL DEFAULT NULL,
                                    `updated_at` timestamp NULL DEFAULT NULL,
                                    `deleted_at` timestamp NULL DEFAULT NULL,
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `contact_messages`
--

/*!40000 ALTER TABLE `contact_messages` DISABLE KEYS */;
/*!40000 ALTER TABLE `contact_messages` ENABLE KEYS */;

--
-- Table structure for table `countries`
--

DROP TABLE IF EXISTS `countries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `countries` (
                             `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                             `short_name` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                             `country_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                             `flag` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                             `slug` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                             `phonecode` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                             `continent` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                             `status` tinyint NOT NULL DEFAULT '1',
                             `deleted_at` timestamp NULL DEFAULT NULL,
                             `created_at` timestamp NULL DEFAULT NULL,
                             `updated_at` timestamp NULL DEFAULT NULL,
                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `countries`
--

/*!40000 ALTER TABLE `countries` DISABLE KEYS */;
/*!40000 ALTER TABLE `countries` ENABLE KEYS */;

--
-- Table structure for table `coupons`
--

DROP TABLE IF EXISTS `coupons`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `coupons` (
                           `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                           `product_id` int NOT NULL,
                           `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                           `code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                           `discount_type` tinyint NOT NULL DEFAULT '0',
                           `discount` int NOT NULL DEFAULT '0',
                           `redemption_type` int NOT NULL DEFAULT '0',
                           `product_plan` int NOT NULL DEFAULT '0',
                           `valid_date` date NOT NULL,
                           `maximum_redemption` int NOT NULL DEFAULT '0',
                           `status` tinyint NOT NULL DEFAULT '1',
                           `user_id` int NOT NULL DEFAULT '0',
                           `deleted_at` timestamp NULL DEFAULT NULL,
                           `created_at` timestamp NULL DEFAULT NULL,
                           `updated_at` timestamp NULL DEFAULT NULL,
                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `coupons`
--

/*!40000 ALTER TABLE `coupons` DISABLE KEYS */;
/*!40000 ALTER TABLE `coupons` ENABLE KEYS */;

--
-- Table structure for table `currencies`
--

DROP TABLE IF EXISTS `currencies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `currencies` (
                              `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                              `currency_code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                              `symbol` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                              `currency_placement` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1',
                              `current_currency` smallint NOT NULL DEFAULT '0',
                              `deleted_at` timestamp NULL DEFAULT NULL,
                              `created_at` timestamp NULL DEFAULT NULL,
                              `updated_at` timestamp NULL DEFAULT NULL,
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `currencies`
--

/*!40000 ALTER TABLE `currencies` DISABLE KEYS */;
INSERT INTO `currencies` VALUES (1,'USD','$','before',1,NULL,'2024-03-14 01:59:22','2024-03-14 01:59:22'),(2,'BDT','৳','before',0,NULL,'2024-03-14 01:59:22','2024-03-14 01:59:22'),(3,'INR','₹','before',0,NULL,'2024-03-14 01:59:22','2024-03-14 01:59:22'),(4,'GBP','£','after',0,NULL,'2024-03-14 01:59:22','2024-03-14 01:59:22'),(5,'MXN','$','before',0,NULL,'2024-03-14 01:59:22','2024-03-14 01:59:22'),(6,'SAR','SR','before',0,NULL,'2024-03-14 01:59:22','2024-03-14 01:59:22');
/*!40000 ALTER TABLE `currencies` ENABLE KEYS */;

--
-- Table structure for table `database_backup_cron_settings`
--

DROP TABLE IF EXISTS `database_backup_cron_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `database_backup_cron_settings` (
                                                 `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                                 `status` enum('active','inactive') COLLATE utf8mb4_unicode_ci NOT NULL,
                                                 `hour_of_day` time NOT NULL DEFAULT '00:00:00',
                                                 `backup_after_days` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                                 `delete_backup_after_days` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                                 `created_at` timestamp NULL DEFAULT NULL,
                                                 `updated_at` timestamp NULL DEFAULT NULL,
                                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `database_backup_cron_settings`
--

/*!40000 ALTER TABLE `database_backup_cron_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `database_backup_cron_settings` ENABLE KEYS */;

--
-- Table structure for table `database_backups`
--

DROP TABLE IF EXISTS `database_backups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `database_backups` (
                                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                    `filename` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                    `size` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                    `created_at` timestamp NULL DEFAULT NULL,
                                    `updated_at` timestamp NULL DEFAULT NULL,
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `database_backups`
--

/*!40000 ALTER TABLE `database_backups` DISABLE KEYS */;
/*!40000 ALTER TABLE `database_backups` ENABLE KEYS */;

--
-- Table structure for table `email_templates`
--

DROP TABLE IF EXISTS `email_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_templates` (
                                   `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                   `user_id` bigint unsigned NOT NULL,
                                   `category` tinyint DEFAULT NULL,
                                   `subject` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                   `body` longtext COLLATE utf8mb4_unicode_ci,
                                   `status` tinyint NOT NULL DEFAULT '0',
                                   `deleted_at` timestamp NULL DEFAULT NULL,
                                   `created_at` timestamp NULL DEFAULT NULL,
                                   `updated_at` timestamp NULL DEFAULT NULL,
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_templates`
--

/*!40000 ALTER TABLE `email_templates` DISABLE KEYS */;
/*!40000 ALTER TABLE `email_templates` ENABLE KEYS */;

--
-- Table structure for table `failed_jobs`
--

DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `failed_jobs` (
                               `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                               `uuid` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                               `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
                               `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
                               `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
                               `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
                               `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                               PRIMARY KEY (`id`),
                               UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `failed_jobs`
--

/*!40000 ALTER TABLE `failed_jobs` DISABLE KEYS */;
/*!40000 ALTER TABLE `failed_jobs` ENABLE KEYS */;

--
-- Table structure for table `faqs`
--

DROP TABLE IF EXISTS `faqs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `faqs` (
                        `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                        `title` text COLLATE utf8mb4_unicode_ci NOT NULL,
                        `description` longtext COLLATE utf8mb4_unicode_ci,
                        `status` tinyint NOT NULL DEFAULT '1',
                        `created_at` timestamp NULL DEFAULT NULL,
                        `updated_at` timestamp NULL DEFAULT NULL,
                        `deleted_at` timestamp NULL DEFAULT NULL,
                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `faqs`
--

/*!40000 ALTER TABLE `faqs` DISABLE KEYS */;
/*!40000 ALTER TABLE `faqs` ENABLE KEYS */;

--
-- Table structure for table `features_settings`
--

DROP TABLE IF EXISTS `features_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `features_settings` (
                                     `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                     `title` text COLLATE utf8mb4_unicode_ci NOT NULL,
                                     `description` longtext COLLATE utf8mb4_unicode_ci,
                                     `image` int DEFAULT NULL,
                                     `icon` int DEFAULT NULL,
                                     `status` tinyint NOT NULL DEFAULT '1',
                                     `created_at` timestamp NULL DEFAULT NULL,
                                     `updated_at` timestamp NULL DEFAULT NULL,
                                     `deleted_at` timestamp NULL DEFAULT NULL,
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `features_settings`
--

/*!40000 ALTER TABLE `features_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `features_settings` ENABLE KEYS */;

--
-- Table structure for table `file_managers`
--

DROP TABLE IF EXISTS `file_managers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `file_managers` (
                                 `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                 `file_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
                                 `storage_type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                                 `original_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                                 `file_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                                 `user_id` bigint unsigned DEFAULT NULL,
                                 `path` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                                 `extension` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                                 `size` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
                                 `external_link` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 `deleted_at` timestamp NULL DEFAULT NULL,
                                 `created_at` timestamp NULL DEFAULT NULL,
                                 `updated_at` timestamp NULL DEFAULT NULL,
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY `file_managers_file_name_unique` (`file_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `file_managers`
--

/*!40000 ALTER TABLE `file_managers` DISABLE KEYS */;
/*!40000 ALTER TABLE `file_managers` ENABLE KEYS */;

--
-- Table structure for table `frontend_sections`
--

DROP TABLE IF EXISTS `frontend_sections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `frontend_sections` (
                                     `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                     `name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                     `page_title` text COLLATE utf8mb4_unicode_ci,
                                     `title` text COLLATE utf8mb4_unicode_ci,
                                     `slug` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                     `has_page_title` tinyint DEFAULT NULL,
                                     `has_banner_image` tinyint NOT NULL DEFAULT '0',
                                     `has_image` tinyint NOT NULL DEFAULT '0',
                                     `has_description` tinyint NOT NULL DEFAULT '0',
                                     `description` longtext COLLATE utf8mb4_unicode_ci,
                                     `banner_image` int DEFAULT NULL,
                                     `image` int DEFAULT NULL,
                                     `status` tinyint NOT NULL DEFAULT '0',
                                     `created_at` timestamp NULL DEFAULT NULL,
                                     `updated_at` timestamp NULL DEFAULT NULL,
                                     `deleted_at` timestamp NULL DEFAULT NULL,
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `frontend_sections`
--

/*!40000 ALTER TABLE `frontend_sections` DISABLE KEYS */;
INSERT INTO `frontend_sections` VALUES (1,'Hero Banner','Banner page title','Subscription & Billing management software.','hero_banner',0,1,1,1,'Welcome to the future of revenue management! Our subscription billing software is here to transform the way you handle billing and drive your future business to new heights.',NULL,NULL,1,'2024-03-14 01:59:22','2024-03-14 01:59:22',NULL),(2,'Core Features','Core Features','Feature Fusion: Your All-in-One Solution','core_features',1,0,0,0,'Welcome to the future of revenue management! Our subscription billing software is here to transform the way you handle billing and drive your future business to new heights.',NULL,NULL,1,'2024-03-14 01:59:22','2024-03-14 01:59:22',NULL),(3,'Best Features','Best Features','features that you will get after getting started.','best_features',1,0,0,1,'Feugiat scelerisque varius morbi enim nunc faucibus a pellentesque . Ante in nibh mauris cursus mattis molestie. Sagittis vitae et leo duis ut. Lobortis scelerisque fermentum.',NULL,NULL,1,'2024-03-14 01:59:22','2024-03-14 01:59:22',NULL),(4,'Pricing Plan','Pricing Plan','Pick the plan that\'s right for your business.','pricing_plan',1,0,0,0,'Welcome to the future of revenue management! Our subscription billing software is here to transform the way you handle billing and drive your future business to new heights.',NULL,NULL,1,'2024-03-14 01:59:22','2024-03-14 01:59:22',NULL),(5,'Product Services','Product Services','Collect Payments for Your Products and Services.','product_services',1,0,1,0,'Welcome to the future of revenue management! Our subscription billing software is here to transform the way you handle billing and drive your future business to new heights.',NULL,NULL,1,'2024-03-14 01:59:22','2024-03-14 01:59:22',NULL),(6,'Integrations Menu','Integrations','make Seamless Integration with some of best apps.','integrations_menu',1,0,1,1,'Odio euismod lacinia at quis risu sed. Etiam erat velit sceleris que in. Blandit turpis cursu in hac. Porttitor rhoncus dolor purus non enim praesent elementum. ',NULL,NULL,1,'2024-03-14 01:59:22','2024-03-14 01:59:22',NULL),(7,'Testimonials Area','Testimonials','What Our Clients have Saying About Us.','testimonials_area',1,0,0,1,'Euismod lacinia at quis risu sed. Etiam erat velit sceleris que in. Blandit turpis in hac. Porttitor rhoncus dolor purus enim praesent elementum. ',NULL,NULL,1,'2024-03-14 01:59:22','2024-03-14 01:59:22',NULL),(8,'Faq\'s Area','FAQ\'S','Most common question about saas services.','faqs_area',1,0,0,1,'Euismod lacinia at quis risu sed. Etiam erat velit sceleris que in. Blandit turpis in hac. Porttitor rhoncus dolor purus enim praesent elementum. ',NULL,NULL,1,'2024-03-14 01:59:22','2024-03-14 01:59:22',NULL);
/*!40000 ALTER TABLE `frontend_sections` ENABLE KEYS */;

--
-- Table structure for table `gateway_currencies`
--

DROP TABLE IF EXISTS `gateway_currencies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gateway_currencies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `gateway_id` bigint unsigned NOT NULL,
  `currency` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USD',
  `conversion_rate` decimal(8,2) NOT NULL DEFAULT '1.00',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gateway_currencies`
--

/*!40000 ALTER TABLE `gateway_currencies` DISABLE KEYS */;
INSERT INTO `gateway_currencies` (id, user_id, gateway_id, currency, conversion_rate, created_at, updated_at, deleted_at) VALUES
(1, 1, 1, 'USD', 1.00, NULL, NULL, NULL),
(2, 1, 2, 'USD', 1.00, NULL, NULL, NULL),
(3, 1, 3, 'INR', 80.00, NULL, NULL, NULL),
(4, 1, 4, 'INR', 80.00, NULL, NULL, NULL),
(5, 1, 5, 'USD', 1.00, NULL, NULL, NULL),
(6, 1, 6, 'USD', 1.00, NULL, NULL, NULL),
(7, 1, 7, 'NGN', 464.00, NULL, NULL, NULL),
(8, 1, 8, 'BDT', 100.00, NULL, NULL, NULL),
(9, 1, 9, 'NGN', 464.00, NULL, NULL, NULL),
(10, 1, 10, 'BRL', 5.00, NULL, NULL, NULL),
(11, 1, 11, 'USD', 1.00, NULL, NULL, NULL),
(12, 1, 12, 'TRY', 1.00, NULL, NULL, NULL),
(13, 1, 13, 'USD', 1.00, NULL, NULL, NULL),
(14, 1, 14, 'USD', 1.00, NULL, NULL, NULL),
(15, 1, 15, 'INR', 80.00, NULL, NULL, NULL),
(16, 1, 16, 'USD', 1.00, NULL, NULL, NULL),
(17, 1, 17, 'NGN', 464.00, NULL, NULL, NULL),
(18, 1, 18, 'BDT', 100.00, NULL, NULL, NULL),
(19, 1, 19, 'NGN', 464.00, NULL, NULL, NULL),
(20, 1, 20, 'BRL', 5.00, NULL, NULL, NULL),
(21, 1, 21, 'USD', 1.00, NULL, NULL, NULL),
(22, 1, 22, 'TRY', 1.00, NULL, NULL, NULL),
(23, 1, 23, 'USD', 1.00, NULL, NULL, NULL),
(24, 1, 24, 'USD', 1.00, NULL, NULL, NULL),
(25, 1, 25, 'USD', 1.00, NULL, NULL, NULL),
(26, 1, 26, 'MYR', 1.00, NULL, NULL, NULL),
(27, 2, 27, 'USD', 1.00, NULL, NULL, NULL),
(28, 2, 28, 'USD', 1.00, NULL, NULL, NULL),
(29, 2, 29, 'INR', 80.00, NULL, NULL, NULL),
(30, 2, 30, 'INR', 80.00, NULL, NULL, NULL),
(31, 2, 31, 'USD', 1.00, NULL, NULL, NULL),
(32, 2, 32, 'USD', 1.00, NULL, NULL, NULL),
(33, 2, 33, 'NGN', 464.00, NULL, NULL, NULL),
(34, 2, 34, 'BDT', 100.00, NULL, NULL, NULL),
(35, 2, 35, 'NGN', 464.00, NULL, NULL, NULL),
(36, 2, 36, 'BRL', 5.00, NULL, NULL, NULL),
(37, 2, 37, 'USD', 1.00, NULL, NULL, NULL),
(38, 2, 38, 'TRY', 1.00, NULL, NULL, NULL),
(39, 2, 39, 'USD', 1.00, NULL, NULL, NULL),
(40, 2, 40, 'USD', 1.00, NULL, NULL, NULL),
(41, 2, 41, 'INR', 80.00, NULL, NULL, NULL),
(42, 2, 42, 'USD', 1.00, NULL, NULL, NULL),
(43, 2, 43, 'NGN', 464.00, NULL, NULL, NULL),
(44, 2, 44, 'BDT', 100.00, NULL, NULL, NULL),
(45, 2, 45, 'NGN', 464.00, NULL, NULL, NULL),
(46, 2, 46, 'BRL', 5.00, NULL, NULL, NULL),
(47, 2, 47, 'USD', 1.00, NULL, NULL, NULL),
(48, 2, 48, 'TRY', 1.00, NULL, NULL, NULL),
(49, 2, 49, 'USD', 1.00, NULL, NULL, NULL),
(50, 2, 50, 'USD', 1.00, NULL, NULL, NULL),
(51, 2, 51, 'USD', 1.00, NULL, NULL, NULL),
(52, 2, 52, 'MYR', 1.00, NULL, NULL, NULL);
/*!40000 ALTER TABLE `gateway_currencies` ENABLE KEYS */;

--
-- Table structure for table `gateways`
--

DROP TABLE IF EXISTS `gateways`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gateways` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `title` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '1=Active,0=Disable',
  `mode` tinyint NOT NULL DEFAULT '2' COMMENT '1=live,2=sandbox',
  `url` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `key` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'client id, public key, key, store id, api key',
  `secret` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'client secret, secret, store password, auth token',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gateways`
--

/*!40000 ALTER TABLE `gateways` DISABLE KEYS */;
INSERT INTO `gateways` (id, user_id, title, slug, image, status, mode, url, `key`, secret, created_at, updated_at, deleted_at) VALUES
(1, 1, 'Paypal', 'paypal', 'assets/images/gateway-icon/paypal.png', 1, 2, '', '', '', NULL, NULL, NULL),
(2, 1, 'Stripe', 'stripe', 'assets/images/gateway-icon/stripe.png', 1, 2, '', '', '', NULL, NULL, NULL),
(3, 1, 'Razorpay', 'razorpay', 'assets/images/gateway-icon/razorpay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(4, 1, 'Instamojo', 'instamojo', 'assets/images/gateway-icon/instamojo.png', 1, 2, '', '', '', NULL, NULL, NULL),
(5, 1, 'Mollie', 'mollie', 'assets/images/gateway-icon/mollie.png', 1, 2, '', '', '', NULL, NULL, NULL),
(6, 1, 'Coinbase', 'coinbase', 'assets/images/gateway-icon/coinbase.png', 1, 2, '', '', '', NULL, NULL, NULL),
(7, 1, 'Paystack', 'paystack', 'assets/images/gateway-icon/paystack.png', 1, 2, '', '', '', NULL, NULL, NULL),
(8, 1, 'Sslcommerz', 'sslcommerz', 'assets/images/gateway-icon/sslcommerz.png', 1, 2, '', '', '', NULL, NULL, NULL),
(9, 1, 'Flutterwave', 'flutterwave', 'assets/images/gateway-icon/flutterwave.png', 1, 2, '', '', '', NULL, NULL, NULL),
(10, 1, 'Mercadopago', 'mercadopago', 'assets/images/gateway-icon/mercadopago.png', 1, 2, '', '', '', NULL, NULL, NULL),
(11, 1, 'Zitopay', 'zitopay', 'assets/images/gateway-icon/zitopay.png', 1, 1, '', '', '', NULL, NULL, NULL),
(12, 1, 'Iyzipay', 'iyzipay', 'assets/images/gateway-icon/iyzipay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(13, 1, 'Bitpay', 'bitpay', 'assets/images/gateway-icon/bitpay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(14, 1, 'Binance', 'binance', 'assets/images/gateway-icon/binance.png', 1, 2, '', '', '', NULL, NULL, NULL),
(15, 1, 'Paytm', 'paytm', 'assets/images/gateway-icon/paytm.png', 1, 2, '', '', '', NULL, NULL, NULL),
(16, 1, 'PayHere', 'payhere', 'assets/images/gateway-icon/payhere.png', 1, 2, '', '', '', NULL, NULL, NULL),
(17, 1, 'Maxicash', 'maxicash', 'assets/images/gateway-icon/maxicash.png', 1, 2, '', '', '', NULL, NULL, NULL),
(18, 1, 'Cinetpay', 'cinetpay', 'assets/images/gateway-icon/cinetpay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(19, 1, 'VoguePay', 'voguepay', 'assets/images/gateway-icon/voguepay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(20, 1, 'ToyyibPay', 'toyyibpay', 'assets/images/gateway-icon/toyyibpay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(21, 1, 'Paymob', 'paymob', 'assets/images/gateway-icon/paymob.png', 1, 2, '', '', '', NULL, NULL, NULL),
(22, 1, 'AuthorizeNet', 'authorizenet', 'assets/images/gateway-icon/authorizenet.png', 1, 2, '', '', '', NULL, NULL, NULL),
(23, 1, 'Alipay', 'alipay', 'assets/images/gateway-icon/alipay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(24, 1, 'Bank', 'bank', 'assets/images/gateway-icon/bank.png', 1, 2, '', '', '', NULL, NULL, NULL),
(25, 1, 'Cash', 'cash', 'assets/images/gateway-icon/cash.png', 1, 2, '', '', '', NULL, NULL, NULL),
(26, 1, 'Xendit', 'xendit', 'assets/images/gateway-icon/xendit.png', 1, 2, '', '', '', NULL, NULL, NULL),
(27, 2, 'Paypal', 'paypal', 'assets/images/gateway-icon/paypal.png', 1, 2, '', '', '', NULL, NULL, NULL),
(28, 2, 'Stripe', 'stripe', 'assets/images/gateway-icon/stripe.png', 1, 2, '', '', '', NULL, NULL, NULL),
(29, 2, 'Razorpay', 'razorpay', 'assets/images/gateway-icon/razorpay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(30, 2, 'Instamojo', 'instamojo', 'assets/images/gateway-icon/instamojo.png', 1, 2, '', '', '', NULL, NULL, NULL),
(31, 2, 'Mollie', 'mollie', 'assets/images/gateway-icon/mollie.png', 1, 2, '', '', '', NULL, NULL, NULL),
(32, 2, 'Coinbase', 'coinbase', 'assets/images/gateway-icon/coinbase.png', 1, 2, '', '', '', NULL, NULL, NULL),
(33, 2, 'Paystack', 'paystack', 'assets/images/gateway-icon/paystack.png', 1, 2, '', '', '', NULL, NULL, NULL),
(34, 2, 'Sslcommerz', 'sslcommerz', 'assets/images/gateway-icon/sslcommerz.png', 1, 2, '', '', '', NULL, NULL, NULL),
(35, 2, 'Flutterwave', 'flutterwave', 'assets/images/gateway-icon/flutterwave.png', 1, 2, '', '', '', NULL, NULL, NULL),
(36, 2, 'Mercadopago', 'mercadopago', 'assets/images/gateway-icon/mercadopago.png', 1, 2, '', '', '', NULL, NULL, NULL),
(37, 2, 'Zitopay', 'zitopay', 'assets/images/gateway-icon/zitopay.png', 1, 1, '', '', '', NULL, NULL, NULL),
(38, 2, 'Iyzipay', 'iyzipay', 'assets/images/gateway-icon/iyzipay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(39, 2, 'Bitpay', 'bitpay', 'assets/images/gateway-icon/bitpay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(40, 2, 'Binance', 'binance', 'assets/images/gateway-icon/binance.png', 1, 2, '', '', '', NULL, NULL, NULL),
(41, 2, 'Paytm', 'paytm', 'assets/images/gateway-icon/paytm.png', 1, 2, '', '', '', NULL, NULL, NULL),
(42, 2, 'PayHere', 'payhere', 'assets/images/gateway-icon/payhere.png', 1, 2, '', '', '', NULL, NULL, NULL),
(43, 2, 'Maxicash', 'maxicash', 'assets/images/gateway-icon/maxicash.png', 1, 2, '', '', '', NULL, NULL, NULL),
(44, 2, 'Cinetpay', 'cinetpay', 'assets/images/gateway-icon/cinetpay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(45, 2, 'VoguePay', 'voguepay', 'assets/images/gateway-icon/voguepay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(46, 2, 'ToyyibPay', 'toyyibpay', 'assets/images/gateway-icon/toyyibpay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(47, 2, 'Paymob', 'paymob', 'assets/images/gateway-icon/paymob.png', 1, 2, '', '', '', NULL, NULL, NULL),
(48, 2, 'AuthorizeNet', 'authorizenet', 'assets/images/gateway-icon/authorizenet.png', 1, 2, '', '', '', NULL, NULL, NULL),
(49, 2, 'Alipay', 'alipay', 'assets/images/gateway-icon/alipay.png', 1, 2, '', '', '', NULL, NULL, NULL),
(50, 2, 'Bank', 'bank', 'assets/images/gateway-icon/bank.png', 1, 2, '', '', '', NULL, NULL, NULL),
(51, 2, 'Cash', 'cash', 'assets/images/gateway-icon/cash.png', 1, 2, '', '', '', NULL, NULL, NULL),
(52, 2, 'Xendit', 'xendit', 'assets/images/gateway-icon/xendit.png', 1, 2, '', '', '', NULL, NULL, NULL);
/*!40000 ALTER TABLE `gateways` ENABLE KEYS */;

--
-- Table structure for table `invoice_settings`
--

DROP TABLE IF EXISTS `invoice_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `invoice_settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `type` tinyint NOT NULL DEFAULT '1',
  `logo` bigint unsigned DEFAULT NULL,
  `title` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_info` text COLLATE utf8mb4_unicode_ci,
  `prefix` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `info_one` text COLLATE utf8mb4_unicode_ci,
  `info_two` text COLLATE utf8mb4_unicode_ci,
  `info_three` text COLLATE utf8mb4_unicode_ci,
  `footer_text` text COLLATE utf8mb4_unicode_ci,
  `column` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoice_settings`
--

/*!40000 ALTER TABLE `invoice_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `invoice_settings` ENABLE KEYS */;

--
-- Table structure for table `invoices`
--

DROP TABLE IF EXISTS `invoices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `invoices` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `payment_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` bigint unsigned NOT NULL DEFAULT '0',
  `customer_id` bigint unsigned NOT NULL DEFAULT '0',
  `invoice_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
  `product_id` int NOT NULL DEFAULT '0',
  `plan_id` int NOT NULL DEFAULT '0',
  `coupon_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subscription_id` int NOT NULL DEFAULT '0',
  `due_date` datetime NOT NULL,
  `coupon_code` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `amount` decimal(12,2) NOT NULL DEFAULT '0.00',
  `tax` decimal(12,2) NOT NULL DEFAULT '0.00',
  `setup_fees` decimal(12,2) NOT NULL DEFAULT '0.00',
  `shipping_charge` decimal(12,2) NOT NULL DEFAULT '0.00',
  `is_mailed` tinyint NOT NULL DEFAULT '0',
  `is_recurring` tinyint NOT NULL DEFAULT '0',
  `payment_status` tinyint NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoices`
--

/*!40000 ALTER TABLE `invoices` DISABLE KEYS */;
/*!40000 ALTER TABLE `invoices` ENABLE KEYS */;

--
-- Table structure for table `languages`
--

DROP TABLE IF EXISTS `languages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `languages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `language` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `iso_code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `flag_id` bigint unsigned DEFAULT NULL,
  `font` bigint unsigned DEFAULT NULL,
  `rtl` tinyint DEFAULT '4',
  `status` tinyint NOT NULL DEFAULT '1',
  `default` tinyint DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `languages_language_unique` (`language`),
  UNIQUE KEY `languages_iso_code_unique` (`iso_code`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `languages`
--

/*!40000 ALTER TABLE `languages` DISABLE KEYS */;
INSERT INTO `languages` VALUES (1,'English','en',NULL,NULL,0,1,1,'2024-03-14 01:59:22','2024-03-14 01:59:22',NULL);
/*!40000 ALTER TABLE `languages` ENABLE KEYS */;

--
-- Table structure for table `licenses`
--

DROP TABLE IF EXISTS `licenses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licenses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `product_plan` int NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '1',
  `user_id` int NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `licenses`
--

/*!40000 ALTER TABLE `licenses` DISABLE KEYS */;
/*!40000 ALTER TABLE `licenses` ENABLE KEYS */;

--
-- Table structure for table `mail_histories`
--

DROP TABLE IF EXISTS `mail_histories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mail_histories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `host` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subject` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `content` text COLLATE utf8mb4_unicode_ci,
  `status` tinyint NOT NULL DEFAULT '1',
  `date` datetime DEFAULT NULL,
  `error` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mail_histories`
--

/*!40000 ALTER TABLE `mail_histories` DISABLE KEYS */;
/*!40000 ALTER TABLE `mail_histories` ENABLE KEYS */;

--
-- Table structure for table `metas`
--

DROP TABLE IF EXISTS `metas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `metas` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `page_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta_title` mediumtext COLLATE utf8mb4_unicode_ci,
  `meta_description` mediumtext COLLATE utf8mb4_unicode_ci,
  `meta_keyword` mediumtext COLLATE utf8mb4_unicode_ci,
  `og_image` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `metas_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `metas`
--

/*!40000 ALTER TABLE `metas` DISABLE KEYS */;
/*!40000 ALTER TABLE `metas` ENABLE KEYS */;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES (1,'2014_10_12_000000_create_users_table',1),(2,'2014_10_12_100000_create_password_reset_tokens_table',1),(3,'2014_10_12_100000_create_password_resets_table',1),(4,'2019_08_19_000000_create_failed_jobs_table',1),(5,'2019_12_14_000001_create_personal_access_tokens_table',1),(6,'2022_06_23_121213_create_settings_table',1),(7,'2022_06_25_104329_create_countries_table',1),(8,'2022_06_25_110824_create_currencies_table',1),(9,'2022_06_25_111037_create_languages_table',1),(10,'2022_11_30_040739_create_gateways_table',1),(11,'2023_01_03_075827_create_gateway_currencies_table',1),(12,'2023_01_05_092212_create_file_managers_table',1),(13,'2023_01_07_120244_create_banks_table',1),(14,'2023_01_30_071830_create_payments_table',1),(15,'2023_05_29_125747_create_contact_messages_table',1),(16,'2023_07_09_100721_create_notifications_table',1),(17,'2023_07_20_052653_create_email_templates_table',1),(18,'2023_07_22_111528_database_backups_table',1),(19,'2023_07_22_111738_database_backup_cron_settings_table',1),(20,'2023_08_07_062359_create_authentication_log_table',1),(21,'2023_08_26_075204_create_metas_table',1),(22,'2023_09_05_090819_create_notification_seens_table',1),(23,'2023_09_26_055112_create_products_table',1),(24,'2023_09_26_093327_create_subscriptions_table',1),(25,'2023_09_26_112059_create_user_details_table',1),(26,'2023_09_26_132437_create_plans_table',1),(27,'2023_09_27_071617_create_mail_histories_table',1),(28,'2023_09_27_114312_create_checkout_page_settings_table',1),(29,'2023_10_01_093154_create_coupons_table',1),(30,'2023_10_01_110337_create_orders_table',1),(31,'2023_10_02_055452_create_invoices_table',1),(32,'2023_10_02_070636_create_licenses_table',1),(33,'2023_10_04_065739_create_tax_settings_table',1),(34,'2023_10_05_105255_create_webhooks_table',1),(35,'2023_10_08_074534_create_webhook_events_table',1),(36,'2023_10_10_160043_create_invoice_settings_table',1),(37,'2023_10_23_093637_create_packages_table',2),(38,'2023_10_23_094232_create_user_packages_table',2),(39,'2023_10_23_105532_create_subscription_orders_table',2),(40,'2023_10_25_075216_create_frontend_sections_table',2),(41,'2023_10_25_125314_create_features_settings_table',2),(42,'2023_10_26_110108_create_best_features_settings_table',2),(43,'2023_10_26_122659_create_testimonials_table',2),(44,'2023_10_26_124142_create_faqs_table',2),(45,'2023_10_31_063626_add_dependency_field_for_saas',2),(46,'2023_11_13_130122_add_dependency_field_for_affiliate',3),(47,'2023_11_15_054606_create_affiliate_configs_table',3),(48,'2023_11_18_112911_create_affiliate_histories_table',3),(49,'2023_11_19_061425_create_beneficiaries_table',3),(50,'2023_11_19_061522_create_withdraws_table',3),(51,'2023_11_19_062635_create_transactions_table',3),(52,'2019_12_22_015115_create_short_urls_table',4),(53,'2019_12_22_015214_create_short_url_visits_table',4),(54,'2020_02_11_224848_update_short_url_table_for_version_two_zero_zero',4),(55,'2020_02_12_008432_update_short_url_visits_table_for_version_two_zero_zero',4),(56,'2020_04_10_224546_update_short_url_table_for_version_three_zero_zero',4),(57,'2020_04_20_009283_update_short_url_table_add_option_to_forward_query_params',4),(58,'2024_01_09_132126_add_new_field_to_plans_table',4),(59,'2024_03_09_095806_create_user_activity_logs_table',4),(60,'2024_11_14_124513_add_payment_id_in_invoice_table',5),(61,'2024_11_14_124513_add_stripe_id_and_paypal_id_to_plans_table',6),(62,'2024_12_02_071712_create_package_gateway_prices_table',6),(63,'2024_12_04_122024_add_column_in_withdraws_table',6);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;

--
-- Table structure for table `notification_seens`
--

DROP TABLE IF EXISTS `notification_seens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notification_seens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `notification_id` int DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notification_seens`
--

/*!40000 ALTER TABLE `notification_seens` DISABLE KEYS */;
/*!40000 ALTER TABLE `notification_seens` ENABLE KEYS */;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notifications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `title` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `body` text COLLATE utf8mb4_unicode_ci,
  `link` text COLLATE utf8mb4_unicode_ci,
  `view_status` tinyint NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '1',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `orders` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `customer_id` bigint unsigned DEFAULT NULL,
  `product_id` int DEFAULT NULL,
  `transaction_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bank_id` tinyint DEFAULT NULL,
  `plan_id` bigint DEFAULT NULL,
  `invoice_id` bigint DEFAULT NULL,
  `gateway_id` bigint DEFAULT NULL,
  `subscription_id` bigint DEFAULT NULL,
  `order_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `amount` decimal(12,2) NOT NULL DEFAULT '0.00',
  `discount` decimal(12,2) NOT NULL DEFAULT '0.00',
  `discount_type` tinyint NOT NULL DEFAULT '0',
  `shipping_cost` decimal(12,2) NOT NULL DEFAULT '0.00',
  `setup_fees` decimal(12,2) NOT NULL DEFAULT '0.00',
  `tax_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
  `tax_type` int NOT NULL DEFAULT '0',
  `conversion_rate` decimal(12,2) NOT NULL DEFAULT '0.00',
  `platform_charge` decimal(12,2) NOT NULL DEFAULT '0.00',
  `subtotal` decimal(12,2) NOT NULL DEFAULT '0.00',
  `total` decimal(12,2) NOT NULL DEFAULT '0.00',
  `transaction_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
  `order_number` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_status` tinyint NOT NULL DEFAULT '1',
  `delivery_status` tinyint NOT NULL DEFAULT '1',
  `system_currency` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gateway_currency` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bank_deposit_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bank_deposit_slip_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `orders`
--

/*!40000 ALTER TABLE `orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `orders` ENABLE KEYS */;

--
-- Table structure for table `packages`
--

DROP TABLE IF EXISTS `packages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `packages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `customer_limit` int NOT NULL DEFAULT '-1',
  `product_limit` int NOT NULL DEFAULT '-1',
  `subscription_limit` int NOT NULL DEFAULT '-1',
  `icon_id` int DEFAULT NULL,
  `others` text COLLATE utf8mb4_unicode_ci,
  `monthly_price` decimal(12,2) NOT NULL DEFAULT '0.00',
  `yearly_price` decimal(12,2) NOT NULL DEFAULT '0.00',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT 'active for 1 , deactivate for 0',
  `is_default` tinyint NOT NULL DEFAULT '0' COMMENT 'active for 1 , deactivate for 0',
  `is_trail` tinyint NOT NULL DEFAULT '0' COMMENT 'active for 1 , deactivate for 0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `packages`
--

/*!40000 ALTER TABLE `packages` DISABLE KEYS */;
/*!40000 ALTER TABLE `packages` ENABLE KEYS */;

--
-- Table structure for table `password_reset_tokens`
--

DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `password_reset_tokens`
--

/*!40000 ALTER TABLE `password_reset_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `password_reset_tokens` ENABLE KEYS */;

--
-- Table structure for table `password_resets`
--

DROP TABLE IF EXISTS `password_resets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_resets` (
  `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `otp_expiry` datetime DEFAULT NULL,
  `otp` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `password_resets`
--

/*!40000 ALTER TABLE `password_resets` DISABLE KEYS */;
/*!40000 ALTER TABLE `password_resets` ENABLE KEYS */;

--
-- Table structure for table `payments`
--

DROP TABLE IF EXISTS `payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `paymentable_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `paymentable_type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `gateway_id` bigint unsigned NOT NULL,
  `paymentId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tnxId` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` bigint unsigned NOT NULL,
  `bank_id` bigint unsigned DEFAULT NULL,
  `deposit_slip` int DEFAULT NULL,
  `sub_total` decimal(12,2) NOT NULL DEFAULT '0.00',
  `tax` decimal(12,2) NOT NULL DEFAULT '0.00',
  `system_currency` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_currency` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `conversion_rate` decimal(18,8) NOT NULL DEFAULT '0.********',
  `grand_total_with_conversation_rate` decimal(18,8) NOT NULL DEFAULT '0.********',
  `grand_total` decimal(12,2) NOT NULL DEFAULT '0.00',
  `payment_details` longtext COLLATE utf8mb4_unicode_ci,
  `gateway_callback_details` longtext COLLATE utf8mb4_unicode_ci,
  `payment_time` datetime DEFAULT NULL,
  `payment_status` tinyint NOT NULL DEFAULT '1',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payments_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payments`
--

/*!40000 ALTER TABLE `payments` DISABLE KEYS */;
/*!40000 ALTER TABLE `payments` ENABLE KEYS */;

--
-- Table structure for table `personal_access_tokens`
--

DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `personal_access_tokens`
--

/*!40000 ALTER TABLE `personal_access_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `personal_access_tokens` ENABLE KEYS */;

--
-- Table structure for table `plans`
--

DROP TABLE IF EXISTS `plans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `plans` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `stripe_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `paypal_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `product_id` int NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `due_day` int NOT NULL,
  `price` decimal(9,2) NOT NULL,
  `billing_cycle` tinyint NOT NULL DEFAULT '0',
  `shipping_charge` bigint unsigned NOT NULL DEFAULT '0',
  `bill` int NOT NULL DEFAULT '0',
  `duration` tinyint NOT NULL DEFAULT '0',
  `number_of_recurring_cycle` int NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '1',
  `free_trail` int NOT NULL DEFAULT '0',
  `setup_fee` decimal(9,2) NOT NULL DEFAULT '0.00',
  `user_id` int NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `details` longtext COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `plans`
--

/*!40000 ALTER TABLE `plans` DISABLE KEYS */;
/*!40000 ALTER TABLE `plans` ENABLE KEYS */;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` int NOT NULL DEFAULT '1',
  `user_id` int NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products`
--

/*!40000 ALTER TABLE `products` DISABLE KEYS */;
/*!40000 ALTER TABLE `products` ENABLE KEYS */;

--
-- Table structure for table `settings`
--

DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `option_key` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `option_value` text COLLATE utf8mb4_unicode_ci,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `settings`
--

/*!40000 ALTER TABLE `settings` DISABLE KEYS */;
INSERT INTO `settings` VALUES (1,'build_version','14',NULL,'2024-03-14 01:59:22','2024-03-14 01:59:22'),(2,'current_version','4.2',NULL,'2024-03-14 01:59:22','2024-03-14 01:59:22');
/*!40000 ALTER TABLE `settings` ENABLE KEYS */;

--
-- Table structure for table `short_url_visits`
--

DROP TABLE IF EXISTS `short_url_visits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `short_url_visits` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `short_url_id` bigint unsigned NOT NULL,
  `ip_address` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `operating_system` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `operating_system_version` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `browser` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `browser_version` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `referer_url` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device_type` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `visited_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `short_url_visits_short_url_id_foreign` (`short_url_id`),
  CONSTRAINT `short_url_visits_short_url_id_foreign` FOREIGN KEY (`short_url_id`) REFERENCES `short_urls` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `short_url_visits`
--

/*!40000 ALTER TABLE `short_url_visits` DISABLE KEYS */;
/*!40000 ALTER TABLE `short_url_visits` ENABLE KEYS */;

--
-- Table structure for table `short_urls`
--

DROP TABLE IF EXISTS `short_urls`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `short_urls` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `destination_url` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `url_key` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `default_short_url` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `single_use` tinyint(1) NOT NULL,
  `forward_query_params` tinyint(1) NOT NULL DEFAULT '0',
  `track_visits` tinyint(1) NOT NULL,
  `redirect_status_code` int NOT NULL DEFAULT '301',
  `track_ip_address` tinyint(1) NOT NULL DEFAULT '0',
  `track_operating_system` tinyint(1) NOT NULL DEFAULT '0',
  `track_operating_system_version` tinyint(1) NOT NULL DEFAULT '0',
  `track_browser` tinyint(1) NOT NULL DEFAULT '0',
  `track_browser_version` tinyint(1) NOT NULL DEFAULT '0',
  `track_referer_url` tinyint(1) NOT NULL DEFAULT '0',
  `track_device_type` tinyint(1) NOT NULL DEFAULT '0',
  `activated_at` timestamp NULL DEFAULT '2024-03-14 01:59:20',
  `deactivated_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `short_urls_url_key_unique` (`url_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `short_urls`
--

/*!40000 ALTER TABLE `short_urls` DISABLE KEYS */;
/*!40000 ALTER TABLE `short_urls` ENABLE KEYS */;

--
-- Table structure for table `subscription_orders`
--

DROP TABLE IF EXISTS `subscription_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subscription_orders` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `package_id` bigint unsigned NOT NULL,
  `order_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `duration_type` tinyint NOT NULL DEFAULT '1',
  `payment_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `transaction_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `discount` decimal(12,2) NOT NULL DEFAULT '0.00',
  `discount_type` tinyint NOT NULL DEFAULT '0',
  `amount` decimal(12,2) NOT NULL DEFAULT '0.00',
  `tax_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
  `tax_type` tinyint NOT NULL DEFAULT '1',
  `subtotal` decimal(12,2) NOT NULL DEFAULT '0.00',
  `total` decimal(12,2) DEFAULT '0.00',
  `transaction_amount` decimal(12,2) DEFAULT '0.00',
  `system_currency` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gateway_id` bigint unsigned NOT NULL,
  `gateway_currency` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `conversion_rate` decimal(12,2) DEFAULT '1.00',
  `payment_status` tinyint NOT NULL DEFAULT '0' COMMENT '0=pending, 1=paid, 2=cancelled',
  `bank_id` bigint unsigned DEFAULT NULL,
  `bank_deposit_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bank_deposit_slip_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `subscription_orders`
--

/*!40000 ALTER TABLE `subscription_orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `subscription_orders` ENABLE KEYS */;

--
-- Table structure for table `subscriptions`
--

DROP TABLE IF EXISTS `subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subscriptions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `plan_id` int NOT NULL,
  `subscription_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `license` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` int NOT NULL,
  `customer_id` int NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `due_day` int NOT NULL DEFAULT '0',
  `amount` decimal(12,2) NOT NULL,
  `free_trail` int NOT NULL DEFAULT '0',
  `setup_fee` decimal(9,2) NOT NULL DEFAULT '0.00',
  `billing_cycle` tinyint NOT NULL DEFAULT '0',
  `bill` int NOT NULL DEFAULT '1',
  `duration` tinyint NOT NULL DEFAULT '0',
  `number_of_recurring_cycle` int NOT NULL DEFAULT '0',
  `shipping_charge` decimal(12,2) NOT NULL DEFAULT '0.00',
  `status` tinyint NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `affiliate_code` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `subscriptions`
--

/*!40000 ALTER TABLE `subscriptions` DISABLE KEYS */;
/*!40000 ALTER TABLE `subscriptions` ENABLE KEYS */;

--
-- Table structure for table `tax_settings`
--

DROP TABLE IF EXISTS `tax_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tax_settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tax_rule_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int NOT NULL,
  `product_id` int NOT NULL,
  `plan_id` int NOT NULL,
  `tax_amount` decimal(8,2) NOT NULL DEFAULT '0.00',
  `status` tinyint NOT NULL DEFAULT '0',
  `tax_type` tinyint NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tax_settings`
--

/*!40000 ALTER TABLE `tax_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `tax_settings` ENABLE KEYS */;

--
-- Table structure for table `testimonials`
--

DROP TABLE IF EXISTS `testimonials`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `testimonials` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `designation` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `comment` tinytext COLLATE utf8mb4_unicode_ci,
  `status` tinyint NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `testimonials`
--

/*!40000 ALTER TABLE `testimonials` DISABLE KEYS */;
/*!40000 ALTER TABLE `testimonials` ENABLE KEYS */;

--
-- Table structure for table `transactions`
--

DROP TABLE IF EXISTS `transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transactions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `order_id` bigint unsigned DEFAULT NULL,
  `reference_id` bigint unsigned DEFAULT NULL,
  `type` tinyint NOT NULL,
  `tnxId` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(12,2) NOT NULL,
  `purpose` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payment_time` datetime NOT NULL,
  `payment_method` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transactions`
--

/*!40000 ALTER TABLE `transactions` DISABLE KEYS */;
/*!40000 ALTER TABLE `transactions` ENABLE KEYS */;

--
-- Table structure for table `user_activity_logs`
--

DROP TABLE IF EXISTS `user_activity_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_activity_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `action` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip_address` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `location` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ticket_id` int DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_activity_logs_user_id_foreign` (`user_id`),
  CONSTRAINT `user_activity_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_activity_logs`
--

/*!40000 ALTER TABLE `user_activity_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_activity_logs` ENABLE KEYS */;

--
-- Table structure for table `user_details`
--

DROP TABLE IF EXISTS `user_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_details` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `basic_info` longtext COLLATE utf8mb4_unicode_ci,
  `basic_first_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `basic_last_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `basic_phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `basic_email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `basic_company` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `billing_info` longtext COLLATE utf8mb4_unicode_ci,
  `billing_first_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `billing_last_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `billing_email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `billing_phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `billing_zip_code` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `billing_address` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `billing_city` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `billing_state` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `billing_country` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shipping_info` longtext COLLATE utf8mb4_unicode_ci,
  `shipping_first_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shipping_last_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shipping_email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shipping_phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shipping_zip_code` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shipping_address` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shipping_city` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shipping_state` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shipping_country` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shipping_method` tinyint DEFAULT NULL,
  `payment` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `revenue` decimal(12,2) NOT NULL DEFAULT '0.00',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_details`
--

/*!40000 ALTER TABLE `user_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_details` ENABLE KEYS */;

--
-- Table structure for table `user_packages`
--

DROP TABLE IF EXISTS `user_packages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_packages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `package_id` bigint unsigned NOT NULL,
  `order_id` bigint unsigned DEFAULT NULL,
  `customer_limit` int NOT NULL DEFAULT '-1',
  `product_limit` int NOT NULL DEFAULT '-1',
  `subscription_limit` int NOT NULL DEFAULT '-1',
  `monthly_price` decimal(12,2) NOT NULL DEFAULT '0.00',
  `yearly_price` decimal(12,2) NOT NULL DEFAULT '0.00',
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `status` tinyint NOT NULL DEFAULT '0',
  `is_trail` tinyint NOT NULL DEFAULT '0' COMMENT 'active for 1 , deactivate for 0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_packages`
--

/*!40000 ALTER TABLE `user_packages` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_packages` ENABLE KEYS */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `nick_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `mobile` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `state` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `zip_code` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `currency` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_designation` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_country` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_state` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_city` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_zip_code` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_address` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_logo` int DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` bigint unsigned DEFAULT NULL,
  `role` tinyint NOT NULL DEFAULT '2',
  `email_verification_status` tinyint NOT NULL DEFAULT '0',
  `phone_verification_status` tinyint NOT NULL DEFAULT '0',
  `google_auth_status` tinyint NOT NULL DEFAULT '0',
  `google2fa_secret` text COLLATE utf8mb4_unicode_ci,
  `google_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `facebook_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `verify_token` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `otp` int DEFAULT NULL,
  `otp_expiry` datetime DEFAULT NULL,
  `last_seen` datetime NOT NULL DEFAULT '2024-03-14 07:59:20',
  `show_email_in_public` tinyint NOT NULL DEFAULT '1',
  `show_phone_in_public` tinyint NOT NULL DEFAULT '1',
  `created_by` bigint unsigned DEFAULT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `affiliate_code` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `affiliate_commission_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_uuid_unique` (`uuid`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'12345','Administrator Doe',NULL,'<EMAIL>','0',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'$2y$10$WbVccI/d625e9jAFFi.u7eUt3UuTngx6V8aoH/gNUzBvB6atGBfYG',NULL,1,1,1,0,'YWWCMGVRD7M4BCA7',NULL,NULL,NULL,NULL,NULL,'2024-03-14 07:59:20',1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,0.00),(2,'123455','User Doe',NULL,'<EMAIL>','1',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'$2y$10$H/tOeZ2CGUOL4TwSj2qne.L3OdAnlkfrPA1CMyH6G4qysjUFrU8fa',NULL,2,1,1,0,'V7UBLYDHJKLGQJZD',NULL,NULL,NULL,NULL,NULL,'2024-03-14 07:59:20',1,1,NULL,1,NULL,NULL,NULL,NULL,NULL,0.00);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;

--
-- Table structure for table `webhook_events`
--

DROP TABLE IF EXISTS `webhook_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `webhook_events` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `event_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `event_type` tinyint NOT NULL,
  `user_id` int NOT NULL,
  `webhook_id` int NOT NULL,
  `product_id` int NOT NULL,
  `plan_id` int NOT NULL,
  `request_data` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `webhook_url` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `response_msg` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `response_code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `retry_count` int NOT NULL,
  `response_data` text COLLATE utf8mb4_unicode_ci,
  `status` tinyint NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `webhook_events`
--

/*!40000 ALTER TABLE `webhook_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `webhook_events` ENABLE KEYS */;

--
-- Table structure for table `webhooks`
--

DROP TABLE IF EXISTS `webhooks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `webhooks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `webhook_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int NOT NULL,
  `product_id` int NOT NULL,
  `plan_id` int NOT NULL,
  `webhook_url` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `webhooks`
--

/*!40000 ALTER TABLE `webhooks` DISABLE KEYS */;
/*!40000 ALTER TABLE `webhooks` ENABLE KEYS */;

--
-- Table structure for table `withdraws`
--

DROP TABLE IF EXISTS `withdraws`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `withdraws` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `tenant_id` bigint unsigned DEFAULT NULL,
  `beneficiary_id` bigint unsigned DEFAULT NULL,
  `tnxId` varchar(80) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(12,2) NOT NULL DEFAULT '0.00',
  `payment_method` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `note` mediumtext COLLATE utf8mb4_unicode_ci,
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '0=pending, 1=complete, 2=rejected',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
--
-- Table structure for table `package_gateway_prices`
--

DROP TABLE IF EXISTS `package_gateway_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `package_gateway_prices` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `gateway_id` bigint unsigned NOT NULL,
  `gateway_currency_id` bigint unsigned NOT NULL,
  `package_id` bigint unsigned NOT NULL,
  `gateway` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `monthly_price_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `yearly_price_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `package_gateway_prices`
--

/*!40000 ALTER TABLE `withdraws` DISABLE KEYS */;
/*!40000 ALTER TABLE `withdraws` ENABLE KEYS */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-03-14 13:59:47
