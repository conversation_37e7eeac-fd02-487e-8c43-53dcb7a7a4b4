<?php $__env->startSection('content'); ?>
    <!-- Start Core features -->
    <?php if(isset($section['core_features']) && $section['core_features']->status == STATUS_ACTIVE): ?>
        <section class="py-150 ld-container-1320" id="features">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-6">
                        <div class="text-center pb-45">
                            <p
                                class="d-inline-flex py-6 px-20 bd-ra-12 bg-main-color-20 mb-13 fs-17 fw-500 lh-28 text-main-color">
                                <?php echo e($section['core_features']->page_title); ?></p>
                            <h4 class="fs-sm-64 fs-33 fw-700 lh-sm-76 lh-44 text-textBlack">
                                <?php echo e($section['core_features']->title); ?></h4>
                        </div>
                    </div>
                </div>
                <div class="landing-features-one">
                    <?php $__currentLoopData = $feature; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fetures): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="item row align-items-center">
                            <div class="col-lg-5">
                                <div class="max-w-483">
                                    <div class="d-flex max-w-80 pb-27"><img src="<?php echo e(asset(getFileUrl($fetures->icon))); ?>"
                                            alt="" /></div>
                                    <h4 class="fs-sm-52 fw-700 lh-sm-64 text-textBlack pb-9"><?php echo e($fetures->title); ?></h4>
                                    <p class="fs-18 fw-400 lh-28 text-para-text"><?php echo e($fetures->description); ?></p>
                                </div>
                            </div>
                            <div class="col-lg-7">
                                <div class="max-w-680 ms-auto"><img src="<?php echo e(asset(getFileUrl($fetures->image))); ?>"
                                        alt="" /></div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <!-- End Core features -->

    <!-- Start Best features -->
    <?php if(isset($section['best_features']) && $section['best_features']->status == STATUS_ACTIVE): ?>
        <section class="bg-white pt-150 ld-container-1335 best-features">
            <div class="best-features-bg-img">
                <img src="<?php echo e(asset('user/images/landing-page/best-features-bg.png')); ?>" alt="<?php echo e(getOption('app_name')); ?>" />
            </div>
            <div class="container position-relative z-index-1">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="text-center pb-52">
                            <p
                                class="d-inline-flex py-6 px-20 bd-ra-12 bg-main-color-20 mb-13 fs-17 fw-500 lh-28 text-main-color">
                                <?php echo e($section['best_features']->page_title); ?></p>
                            <h4 class="pb-9 fs-sm-64 fs-33 fw-700 lh-sm-76 lh-44 text-white">
                                <<?php echo e($section['best_features']->title); ?> /h4>
                                    <p class="fs-18 fw-400 lh-28 text-header-text max-w-581 m-auto">
                                        <?php echo e($section['best_features']->description); ?></p>
                        </div>
                    </div>
                </div>
                <div class="">
                    <ul class="nav nav-tabs zTab-reset zTab-three" id="myTab" role="tablist">
                        <?php $__empty_1 = true; $__currentLoopData = $best_features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link<?php echo e($key === 0 ? ' active' : ''); ?>" id="tab-<?php echo e($key); ?>-tab"
                                    data-bs-toggle="tab" data-bs-target="#tab-<?php echo e($key); ?>" type="button"
                                    role="tab" aria-controls="tab-<?php echo e($key); ?>"
                                    aria-selected="<?php echo e($key === 0 ? 'true' : 'false'); ?>">
                                    <?php echo e($item->name); ?>

                                </button>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <!-- Handle empty case here -->
                        <?php endif; ?>
                    </ul>
                    <div class="tab-content best-features-tabContent" id="myTabContent">
                        <?php $__empty_1 = true; $__currentLoopData = $best_features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="tab-pane fade<?php echo e($key === 0 ? ' show active' : ''); ?>" id="tab-<?php echo e($key); ?>"
                                role="tabpanel" aria-labelledby="tab-<?php echo e($key); ?>-tab" tabindex="0">
                                <div class="best-features-content">
                                    <div class="row align-items-center rg-20">
                                        <div class="col-lg-5">
                                            <div class="max-w-483">
                                                <h4 class="fs-sm-52 fs-33 fw-700 lh-sm-64 text-white pb-9">
                                                    <?php echo e($item->title); ?></h4>
                                                <p class="fs-18 fw-400 lh-28 text-para-text"><?php echo e($item->description); ?></p>
                                            </div>
                                        </div>
                                        <div class="col-lg-7">
                                            <div class="max-w-680 ms-auto">
                                                <img src="<?php echo e(getFileUrl($item->image)); ?>" alt="<?php echo e($item->title); ?>" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="text-end">
                                <p class="text-main-color"><?php echo e(__('No Data Found')); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <!-- End Best features -->
    <?php if(isset($section['pricing_plan']) && $section['pricing_plan']->status == STATUS_ACTIVE): ?>
        <!-- Start Pricing Plan -->
        <section class="py-150 bg-white ld-container-1320" id="price">
            <div class="container">
                <!--  -->
                <div class="row justify-content-center">
                    <div class="col-lg-7">
                        <div class="text-center pb-35">
                            <p
                                class="d-inline-flex py-6 px-20 bd-ra-12 bg-main-color-20 mb-13 fs-17 fw-500 lh-28 text-main-color">
                                <?php echo e($section['pricing_plan']->page_title); ?></p>
                            <h4 class="fs-sm-64 fs-33 fw-700 lh-sm-76 lh-44 text-textBlack">
                                <?php echo e($section['pricing_plan']->title); ?></h4>
                        </div>
                    </div>
                </div>
                <!--  -->
                <div class="d-flex justify-content-center align-items-center g-20 pb-50">
                    <h4 class="fs-20 fw-500 lh-26 text-textBlack"><?php echo e(__('Monthly')); ?></h4>
                    <div class="price-plan-tab">
                        <div class="zCheck form-check form-switch zPrice-plan-switch">
                            <input class="form-check-input" type="checkbox" role="switch" id="zPrice-plan-switch" />
                        </div>
                    </div>
                    <h4 class="fs-20 fw-500 lh-26 text-textBlack"><?php echo e(__('Yearly')); ?></h4>
                </div>
                <!--  -->
                <div class="">
                    <div class="row rg-20">
                        <?php $__currentLoopData = $packages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-xl-4 col-md-6">
                                <div
                                    class="price-plan-one <?php echo e($package->is_default == ACTIVE ? 'price-plan-popular active' : ''); ?>">
                                    <div class="price-head">
                                        <div class="icon">
                                            <img src="<?php echo e(getFileUrl($package->icon_id)); ?>" alt="<?php echo e($package->name); ?>" />
                                        </div>
                                        <h4 class="fs-24 fw-500 lh-28 text-white pb-19"><?php echo e($package->name); ?></h4>
                                        <h4 class="fs-20 fw-500 lh-24 text-white zPrice-plan-monthly"><span
                                                class="fs-sm-64 lh-sm-76"><?php echo e(showPrice($package->monthly_price)); ?></span>/<?php echo e(__('Monthly')); ?>

                                        </h4>
                                        <h4 class="fs-20 fw-500 lh-24 text-white zPrice-plan-yearly"><span
                                                class="fs-sm-64 lh-sm-76"><?php echo e(showPrice($package->yearly_price)); ?></span>/<?php echo e(__('Yearly')); ?>

                                        </h4>
                                    </div>
                                    <div class="price-body">
                                        <ul class="zList-pb-13 mb-50">
                                            <li>
                                                <div class="d-flex align-items-center g-10 bg-white bd-ra-40 py-12 px-15">
                                                    <div class="d-flex max-w-22">
                                                        <img src="<?php echo e(asset('user/images/icon/price-check-icon.svg')); ?>"
                                                            alt="<?php echo e($package->name); ?>" />
                                                    </div>
                                                    <p class="fs-18 fw-400 lh-22 text-para-text">
                                                        <?php if($package->customer_limit == -1): ?>
                                                            <?php echo e(__('Add Unlimited Customers')); ?>

                                                        <?php else: ?>
                                                            <?php echo e(__('Add ' . $package->customer_limit . ' Customers')); ?>

                                                        <?php endif; ?>
                                                    </p>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="d-flex align-items-center g-10 bg-white bd-ra-40 py-12 px-15">
                                                    <div class="d-flex max-w-22">
                                                        <img src="<?php echo e(asset('user/images/icon/price-check-icon.svg')); ?>"
                                                            alt="<?php echo e($package->name); ?>" />
                                                    </div>
                                                    <p class="fs-18 fw-400 lh-22 text-para-text">
                                                        <?php if($package->product_limit == -1): ?>
                                                            <?php echo e(__('Add Unlimited Products')); ?>

                                                        <?php else: ?>
                                                            <?php echo e(__('Add ' . $package->product_limit . ' Products')); ?>

                                                        <?php endif; ?>
                                                    </p>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="d-flex align-items-center g-10 bg-white bd-ra-40 py-12 px-15">
                                                    <div class="d-flex max-w-22">
                                                        <img src="<?php echo e(asset('user/images/icon/price-check-icon.svg')); ?>"
                                                            alt="<?php echo e($package->name); ?>" />
                                                    </div>
                                                    <p class="fs-18 fw-400 lh-22 text-para-text">
                                                        <?php if($package->customer_limit == -1): ?>
                                                            <?php echo e(__('Add Unlimited Subscriptions')); ?>

                                                        <?php else: ?>
                                                            <?php echo e(__('Add ' . $package->customer_limit . ' Subscriptions')); ?>

                                                        <?php endif; ?>
                                                    </p>
                                                </div>
                                            </li>
                                            <?php $__currentLoopData = json_decode($package->others) ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $other): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li>
                                                    <div
                                                        class="d-flex align-items-center g-10 bg-white bd-ra-40 py-12 px-15">
                                                        <div class="d-flex max-w-22">
                                                            <img src="<?php echo e(asset('user/images/icon/price-check-icon.svg')); ?>"
                                                                alt="<?php echo e($package->name); ?>" />
                                                        </div>
                                                        <p class="fs-18 fw-400 lh-22 text-para-text">
                                                            <?php echo e(__($other)); ?> </p>
                                                    </div>
                                                </li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>
                                        <a href="<?php echo e(route('user.subscription.index')); ?>?id=<?php echo e($package->id); ?>"
                                            class="btn link"
                                            title="<?php echo e(__('Subscribe Now')); ?>"><?php echo e(__('Subscribe Now')); ?></a>
                                    </div>
                                </div>
                                </form>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <!-- End Pricing Plan -->
    <?php if(isset($section['product_services']) && $section['product_services']->status == STATUS_ACTIVE): ?>
        <!-- Start Product Services -->
        <section class="pb-150 bg-white" id="products">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="text-center pb-45">
                            <p
                                class="d-inline-flex py-6 px-20 bd-ra-12 bg-main-color-20 mb-13 fs-17 fw-500 lh-28 text-main-color">
                                <?php echo e($section['product_services']->page_title); ?></p>
                            <h4 class="pb-9 fs-sm-64 fs-33 fw-700 lh-sm-76 lh-44 text-textBlack">
                                <?php echo e($section['product_services']->title); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="container-fluid">
                <div class="max-w-1701 m-auto">
                    <img src="<?php echo e(asset(getFileUrl($section['product_services']->image))); ?>"
                        alt="<?php echo e($section['product_services']->page_title); ?>" />
                </div>
            </div>
        </section>
    <?php endif; ?>
    <!-- End Product Services -->
    <!-- Start integrations -->
    <?php if(isset($section['integrations_menu']) && $section['integrations_menu']->status == STATUS_ACTIVE): ?>
        <section class="py-150" id="integration" data-background="assets/images/landing-page/integrations-bg.png">
            <!--  -->
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="text-center pb-52">
                            <p
                                class="d-inline-flex py-6 px-20 bd-ra-12 bg-main-color-20 mb-13 fs-17 fw-500 lh-28 text-main-color">
                                <?php echo e($section['integrations_menu']->page_title); ?></p>
                            <h4 class="pb-9 fs-sm-64 fs-33 fw-700 lh-sm-76 lh-44 text-textBlack">
                                <?php echo e($section['integrations_menu']->title); ?></h4>
                            <p class="fs-18 fw-400 lh-28 text-para-text"><?php echo e($section['integrations_menu']->description); ?>

                            </p>
                        </div>
                    </div>
                </div>
                <div class="">
                    <img src="<?php echo e(asset(getFileUrl($section['integrations_menu']->image))); ?>"
                        alt="<?php echo e($section['integrations_menu']->page_title); ?>" />
                </div>
            </div>
        </section>
    <?php endif; ?>
    <!-- End integrations -->
    <!-- Start Testimonials -->
    <?php if(isset($section['testimonials_area']) && $section['testimonials_area']->status == STATUS_ACTIVE): ?>
        <section class="pt-150 bg-white overflow-hidden" id="testimonial">
            <div class="container">
                <!--  -->
                <div class="row justify-content-center">
                    <div class="col-lg-7">
                        <div class="text-center pb-52">
                            <p
                                class="d-inline-flex py-6 px-20 bd-ra-12 bg-main-color-20 mb-13 fs-17 fw-500 lh-28 text-main-color">
                                <?php echo e($section['testimonials_area']->page_title); ?></p>
                            <h4 class="pb-9 fs-sm-64 fs-33 fw-700 lh-sm-76 lh-44 text-textBlack">
                                <?php echo e($section['testimonials_area']->title); ?></h4>
                            <p class="fs-18 fw-400 lh-28 text-para-text max-w-581 m-auto">
                                <?php echo e($section['testimonials_area']->description); ?></p>
                        </div>
                    </div>
                </div>
            </div>
            <!--  -->
            <div class="ld-testi-contain">
                <div class="swiper testiSlider">
                    <div class="swiper-wrapper">
                        <?php $__empty_1 = true; $__currentLoopData = $all_testimonial; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="swiper-slide">
                                <div class="bg-body-bg bd-ra-15 px-13 ld-testi-wrap">
                                    <div class="row align-items-center">
                                        <div class="col-lg-6 align-self-stretch">
                                            <div class="ld-testi-img">
                                                <img src="<?php echo e(getFileUrl($testimonial->image)); ?>"
                                                    alt="<?php echo e($testimonial->name); ?>" />
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="py-12">
                                                <div class="ld-testi-content">
                                                    <p
                                                        class="bd-b-one bd-c-stroke-color pb-31 mb-31 fs-20 fw-400 lh-32 text-para-text">
                                                        <?php echo e($testimonial->comment); ?></p>
                                                    <div class="">
                                                        <h4 class="fs-28 fw-600 lh-28 text-textBlack pb-8">
                                                            <?php echo e($testimonial->name); ?></h4>
                                                        <p class="fs-18 fw-500 lh-28 text-textBlack">
                                                            <?php echo e($testimonial->designation); ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="text-end">
                                <p class="text-main-color"><?php echo e(__('No Data Found')); ?></p>
                            </div>
                        <?php endif; ?>

                    </div>
                    <div class="ld-testi-arrow-btn">
                        <div class="swiper-button-next"><i class="fa-solid fa-angle-right"></i></div>
                        <div class="swiper-button-prev"><i class="fa-solid fa-angle-left"></i></div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <!-- End Testimonials -->

    <!-- Start FAQ's -->
    <?php if(isset($section['faqs_area']) && $section['faqs_area']->status == STATUS_ACTIVE): ?>
        <section class="py-150 bg-white" id="faq">
            <div class="container">
                <!--  -->
                <div class="row justify-content-center">
                    <div class="col-lg-7">
                        <div class="text-center pb-52">
                            <p
                                class="d-inline-flex py-6 px-20 bd-ra-12 bg-main-color-20 mb-13 fs-17 fw-500 lh-28 text-main-color">
                                <?php echo e($section['faqs_area']->page_title); ?> </p>
                            <h4 class="pb-9 fs-sm-64 fs-33 fw-700 lh-sm-76 lh-44 text-textBlack">
                                <?php echo e($section['faqs_area']->title); ?></h4>
                            <p class="fs-18 fw-400 lh-28 text-para-text max-w-581 m-auto">
                                <?php echo e($section['faqs_area']->description); ?></p>
                        </div>
                    </div>
                </div>
                <!--  -->
                <div class="accordion zAccordion-reset zAccordion-one" id="accordionExample">
                    <div class="row rg-24">
                        <?php $__empty_1 = true; $__currentLoopData = $all_faq; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="col-lg-6">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#collapse<?php echo e($key); ?>"
                                            aria-controls="collapse<?php echo e($key); ?>"><?php echo e($faq->title); ?></button>
                                    </h2>
                                    <div id="collapse<?php echo e($key); ?>" class="accordion-collapse collapse"
                                        data-bs-parent="#accordionExample">
                                        <div class="accordion-body">
                                            <p class=""><?php echo e($faq->description); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="text-center">
                                <p class="text-main-color"><?php echo e(__('No Data Found')); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <!-- End FAQ's -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('saas.frontend.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaisub\resources\views/saas/frontend/index.blade.php ENDPATH**/ ?>