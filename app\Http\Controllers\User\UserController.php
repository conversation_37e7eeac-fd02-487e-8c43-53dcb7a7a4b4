<?php
namespace App\Http\Controllers\User;
use App\Http\Requests\User\CustomerProfileRequest;
use App\Http\Services\GatewayService;
use App\Http\Services\SettingsService;
use App\Models\Plan;
use Carbon\Carbon;
use App\Models\User;
use Illuminate\Http\Request;
use App\Traits\ResponseTrait;
use PharIo\Manifest\Exception;
use App\Http\Services\UserService;
use App\Mail\UserEmailVerification;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cookie;

class UserController extends Controller
{
    use ResponseTrait;
    public $userService,$settingsService,$gatewayService ;
    public function __construct()
    {
        $this->userService = new UserService();
        $this->settingsService = new SettingsService();
        $this->gatewayService = new GatewayService();
    }

    public function emailVerified($token)
    {
        if (User::where('remember_token', $token)->count() > 0) {
            $user = User::where('remember_token', $token)->first();
            $user->status = USER_STATUS_ACTIVE;
            $user->email_verified_at = Carbon::now()->format("Y-m-d H:i:s");
            $user->email_verification_status = 1;
            $user->save();
            return redirect()->route('login')->with('success', __('Congratulations! Successfully verified your email.'));
        } else {
            return redirect(route('login'));
        }
    }

    public function emailVerify($token)
    {
        try {
            if(!request()->cookie('verify_email_send')){
                $user = User::where('verify_token', $token)->firstOrFail();
                Mail::to($user->email)->send(new UserEmailVerification($user));
                Cookie::queue('verify_email_send', true, 1);
                return redirect()->back()->with('success', __(SENT_SUCCESSFULLY));
            }
            else{
                return redirect()->back()->with('success',__('Already send an email. Please wait a minutes to try another'));
            }
        } catch (Exception $exception) {
            return redirect()->back()->with('error', __(SOMETHING_WENT_WRONG));
        }
    }

    public function emailVerifyResend($token)
    {
        try {
            if (getOption('email_verification_status', 0) == 1) {
                $user = User::where('remember_token', $token)->firstOrFail();
                Mail::to($user->email)->send(new UserEmailVerification($user));
                return redirect()->route('login')->with('success', __(SENT_SUCCESSFULLY));
            } else {
                return redirect()->route('login')->with('error', __(SOMETHING_WENT_WRONG));
            }
        } catch (Exception $e) {
            return redirect()->route('login')->with('error', __(SOMETHING_WENT_WRONG));
        }
    }


    public function customerList(Request $request)
    {
        $data['pageTitle'] = __('Customers');
        $data['activeCustomerList'] = 'active';
        $data['plan'] = Plan::join('products','products.id','plans.product_id')
            ->select(['plans.*','products.name as productName'])
            ->where('plans.status',STATUS_ACTIVE)
            ->where('plans.user_id',auth()->user()->id)->get();
        $data['customer'] = User::where(['role' => USER_ROLE_CUSTOMER, 'status' => STATUS_ACTIVE])->where('created_by',auth()->user()->id)->get();

        if ($request->ajax()) {
            return $this->userService->customerListAll();
        }
        return view('user.customer.index', $data);
    }

    public function customerStore(CustomerProfileRequest $request){

        return $this->userService->customerStore($request);
    }

    public function customerEdit($id){
        $data['pageTitle'] = __('Customer Edit');
        $data['customerData'] = $this->userService->details($id);
        return view('user.customer.edit',$data);
    }

    public function customerDelete($id)
    {
        return $this->userService->delete($id);
    }

    public function customerDetails($id)
    {
        $data['pageTitle'] = __('Customers');
        $data['showCustomerList'] = 'show active';
        $data['activeCustomerList'] = 'active';

        $data['customer_detail'] = $this->userService->details($id);
        return view('user.customer.customer_details', $data);
    }

    public function checkoutOrder(Request $request)
    {
        $request->validate([
            'plan_id' => 'required',
            'customer_id' => 'required',
        ]);

        return $this->userService->checkoutOrder($request);
    }

}
