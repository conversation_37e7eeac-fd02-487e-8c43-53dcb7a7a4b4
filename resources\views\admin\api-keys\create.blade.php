@extends('admin.layouts.master')
@section('title', 'Créer une Clé API')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-flex align-items-center justify-content-between">
                <h4 class="mb-0">Créer une Clé API</h4>
                <div class="page-title-right">
                    <a href="{{ route('admin.api-keys.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form action="{{ route('admin.api-keys.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_id" class="form-label">Utilisateur <span class="text-danger">*</span></label>
                                    <select name="user_id" id="user_id" class="form-select @error('user_id') is-invalid @enderror" required>
                                        <option value="">Sélectionner un utilisateur</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }} ({{ $user->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('user_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nom de la clé <span class="text-danger">*</span></label>
                                    <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror" 
                                           value="{{ old('name') }}" placeholder="Ex: Clé pour application mobile" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="expires_at" class="form-label">Date d'expiration</label>
                                    <input type="datetime-local" name="expires_at" id="expires_at" 
                                           class="form-control @error('expires_at') is-invalid @enderror" 
                                           value="{{ old('expires_at') }}">
                                    <small class="form-text text-muted">Laissez vide pour une clé sans expiration</small>
                                    @error('expires_at')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Permissions</label>
                                    <div class="form-check">
                                        <input type="checkbox" name="permissions[]" value="read" id="permission_read" 
                                               class="form-check-input" {{ in_array('read', old('permissions', [])) ? 'checked' : '' }}>
                                        <label for="permission_read" class="form-check-label">Lecture</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="permissions[]" value="write" id="permission_write" 
                                               class="form-check-input" {{ in_array('write', old('permissions', [])) ? 'checked' : '' }}>
                                        <label for="permission_write" class="form-check-label">Écriture</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="permissions[]" value="delete" id="permission_delete" 
                                               class="form-check-input" {{ in_array('delete', old('permissions', [])) ? 'checked' : '' }}>
                                        <label for="permission_delete" class="form-check-label">Suppression</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="permissions[]" value="admin" id="permission_admin" 
                                               class="form-check-input" {{ in_array('admin', old('permissions', [])) ? 'checked' : '' }}>
                                        <label for="permission_admin" class="form-check-label">Administration</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" name="permissions[]" value="*" id="permission_all" 
                                               class="form-check-input" {{ in_array('*', old('permissions', [])) ? 'checked' : '' }}>
                                        <label for="permission_all" class="form-check-label">Toutes les permissions</label>
                                    </div>
                                    @error('permissions')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> Informations importantes</h6>
                                    <ul class="mb-0">
                                        <li>La clé API sera générée automatiquement lors de la création</li>
                                        <li>La clé sera visible une seule fois lors de la création</li>
                                        <li>Assurez-vous de bien noter la clé car elle ne sera plus affichée ensuite</li>
                                        <li>Vous pourrez régénérer la clé à tout moment depuis la liste</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="{{ route('admin.api-keys.index') }}" class="btn btn-secondary me-2">Annuler</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Créer la clé API
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Auto-select "read" permission by default
    document.addEventListener('DOMContentLoaded', function() {
        const readPermission = document.getElementById('permission_read');
        if (readPermission && !readPermission.checked) {
            readPermission.checked = true;
        }
    });
</script>
@endpush 