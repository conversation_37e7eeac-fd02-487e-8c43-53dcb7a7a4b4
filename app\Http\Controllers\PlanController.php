<?php

namespace App\Http\Controllers;

use App\Http\Requests\User\PlanRequest;
use App\Http\Services\Payment\Payment;
use App\Http\Services\PlanService;
use App\Models\Gateway;
use App\Models\GatewayCurrency;
use App\Models\Plan;
use App\Models\Product;
use App\Models\User;
use App\Traits\ResponseTrait;
use AshAllenDesign\ShortURL\Classes\Builder;
use AshAllenDesign\ShortURL\Models\ShortURL;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Mockery\Exception;

class PlanController extends Controller
{
    use ResponseTrait;

    protected $plan;

    public function __construct()
    {
        $this->plan = new PlanService();
    }

    public function list($id, Request $request)
    {
        if ($request->ajax()) {
            return $this->plan->list($id);
        }
        try {
            $productInfo = Product::find(decrypt($id));
            if (is_null($productInfo)) {
                return $this->error([], getMessage(SOMETHING_WENT_WRONG));
            }
        } catch (Exception $exception) {
            return $this->error([], getMessage(SOMETHING_WENT_WRONG));
        }

        $data['pageTitle'] = __('Plan List For - ') . $productInfo->name;
        $data['activeProduct'] = 'active';
        $data['productInfo'] = $productInfo;
        return view('user.plan.list', $data);
    }

    public function listForDropdown(Request $request)
    {
        $data['plans'] = Plan::where(['product_id' => decrypt($request->product_id), 'user_id' => auth()->id()])->get();
        return view('user.plan.list_for_dropdown', $data);
    }

    public function store(PlanRequest $request)
    {
//        dd($request->all());
        try {
            DB::beginTransaction();
            if (isset($request->id)) {
                $plan = Plan::find(decrypt($request->id));
                $msg = UPDATED_SUCCESSFULLY;
            } else {
                $plan = new Plan();
                $plan->product_id = decrypt($request->product_id);
                $msg = CREATED_SUCCESSFULLY;
            }

            $plan->name = $request->name;
            $plan->code = $request->code;
            $plan->price = $request->price;
            $plan->due_day = $request->due_day;
            $plan->billing_cycle = $request->billing_cycle;
            $plan->bill = $request->bill ?? 0;
            $plan->shipping_charge = $request->shipping_charge ?? 0;
            $plan->duration = $request->duration ?? 0;
            $plan->number_of_recurring_cycle = $request->number_of_recurring_cycle ?? 0;
            $plan->status = $request->status;
            $plan->free_trail = $request->free_trail ?? 0;
            $plan->setup_fee = $request->setup_fee ?? 0;
            $plan->user_id = auth()->id();
            $plan->details = $request->details;
            $plan->save();
            // Dynamically save products to payment gateways
            if (in_array($request->billing_cycle, [BILLING_CYCLE_AUTO_RENEW, BILLING_CYCLE_EXPIRE_AFTER])) {
                $request->validate([
                    'bill' => [
                        'required',
                        'integer',
                        function ($attribute, $value, $fail) use ($request) {
                            if ($request->duration == 2 && $value != 1) {
                                $fail("The $attribute must be 1 if duration is year.");
                            }
                            if ($request->duration == 1 && ($value < 1 || $value > 12)) {
                                $fail("This is maximum 12 if duration is month.");
                            }
                        },
                    ],
                    'duration' => 'required|integer', // Ensures duration is valid
                ]);

                $gateways = RECURRING_GATEWAY;  // Add more gateways here
                $userId = auth()->id();
                foreach ($gateways as $gatewaySlug) {
                    $gateway = Gateway::where(['user_id' => $userId, 'slug' => $gatewaySlug, 'status' => ACTIVE])->first();
                    $gatewayCurrency = GatewayCurrency::where(['gateway_id' => $gateway?->id])->first();
                    if (!is_null($gateway) && !is_null($gatewayCurrency)) {
                        $object = [
                            'webhook_url' => route('payment.webhook.verify', ['payment_method' => $gatewaySlug, 'uid' => $userId]),
                            'currency' => $gatewayCurrency->currency,
                            'type' => 'plan',
                            'user_id' => $userId,
                        ];

                        $paymentService = new Payment($gatewaySlug, $object);
                        // Prepare price data
                        $priceData = [
                            'price' => $plan->price,
                            'duration' => $plan->duration,
                            'bill' => $plan->bill,
                            'price_id' => $gatewaySlug == 'stripe' ? $plan->stripe_id : $plan->paypal_id,
                            'code' => $plan->code,
                            'name' => $plan->name,
                        ];

                        // Save or update prices
                        $priceResponse = $paymentService->saveProduct($priceData);
                        Log::info($priceResponse);
                        if ($priceResponse['success']) {
                            if ($gatewaySlug == 'stripe') {
                                $plan->stripe_id = $priceResponse['data']['price_id'];
                            } else {
                                $plan->paypal_id = $priceResponse['data']['price_id'];
                            }
                            $plan->save();
                        } else {
                            DB::rollBack();
                            return $this->error([], __('Error saving product for ' . ucfirst($gatewaySlug)));
                        }
                    }
                }
            }
            DB::commit();
            return $this->success([], getMessage($msg));
        } catch (Exception $exception) {
            DB::rollBack();
            return $this->error([], getMessage(SOMETHING_WENT_WRONG));
        }
    }

    public function delete($id)
    {
        try {
            $id = decrypt($id);
            $data = Plan::find($id);
            $data->delete();
            return $this->success([], getMessage(DELETED_SUCCESSFULLY));
        } catch (Exception $exception) {
            return $this->error([], getMessage(SOMETHING_WENT_WRONG));
        }
    }

    public function edit($id)
    {
        try {
            $data['plan'] = Plan::find(decrypt($id));
            if (is_null($data['plan'])) {
                return $this->error([], getMessage(SOMETHING_WENT_WRONG));
            }
            return view('user.plan.edit-form', $data)->render();
        } catch (Exception $exception) {
            return $this->error([], getMessage(SOMETHING_WENT_WRONG));
        }
    }

    public function share($id)
    {
        $checkoutData = decrypt($id);
        $data['plan'] = Plan::find($checkoutData['plan_id']);
        if (is_null($data['plan'])) {
            return $this->error([], getMessage(SOMETHING_WENT_WRONG));
        }

        $link = route('checkout', $id);

        try {
            $shortUrl = existingShortUrl($data['plan']->id);
        } catch (\Exception $e) {
            $shortUrl = null;
        }
        if ($shortUrl == null) {
            $builder = new Builder();
            $shortURLObject = $builder->destinationUrl($link)->make();
            $shortUrl = $shortURLObject->default_short_url;
        }

        $data['checkout_url'] = $shortUrl;
        $data['embed_code'] = '<script src="' . url('/') . '/api/checkout/embed.js?embed_code=' . $id . '"></script>';
        $data['fb_share'] = 'http://www.facebook.com/sharer/sharer.php?u=' . $link;
        $data['tw_share'] = 'http://www.twitter.com/share?url=' . $link;
        return view('user.plan.share', $data)->render();

    }

    public function regenerateUrl($id)
    {
        try {
            DB::beginTransaction();

            // Retrieve the plan data
            $data = Plan::findOrFail($id);

            // Retrieve the existing short URL
            $oldUrl = existingShortUrl($data->id);

            if ($oldUrl) {
                // Generate a new unique key
                $newUrlKey = $this->generateUniqueShortURLKey();

                // Build the new URL using the default URL, prefix, and new key
                $newUrl = rtrim(config('short-url.default_url'), '/') . '/' . config('short-url.prefix') . '/' . $newUrlKey;

                // Update the existing short URL in the database
                ShortURL::where('default_short_url', $oldUrl)->update([
                    'default_short_url' => $newUrl,
                    'url_key' => $newUrlKey,
                ]);
            } else {
                // Generate the checkout data and link for a new short URL
                $checkoutData = ['plan_id' => $data->id, 'user_id' => $data->user_id];
                $link = route('checkout', encrypt($checkoutData));

                // Create a new short URL using the Builder
                $builder = new Builder();
                $shortURLObject = $builder->destinationUrl($link)->make();
                $newUrl = $shortURLObject->default_short_url;
            }

            DB::commit();
            return $this->success(['url' => $newUrl], getMessage(UPDATED_SUCCESSFULLY));
        } catch (Exception $exception) {
            DB::rollBack();
            return $this->error([], getMessage(SOMETHING_WENT_WRONG));
        }
    }

    private function generateUniqueShortURLKey()
    {
        // Fetch the configured key length from the config
        $keyLength = config('short-url.key_length', 5); // Default to 5 if not set

        // Generate a unique key
        do {
            $newKey = Str::random($keyLength);
            $existingShortURL = ShortURL::where('url_key', $newKey)->first();
        } while ($existingShortURL);

        // Return the new unique key
        return $newKey;
    }
}
