<?php

namespace App\Http\Controllers\Saas;

use App\Http\Controllers\Controller;
use App\Http\Requests\UserRegisterRequest;
use App\Models\Package;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function user_register_form()
    {
        return view('auth.register');
    }

    public function user_register_store(UserRegisterRequest $request)
    {
        DB::beginTransaction();
        try {
            $google2fa = app('pragmarx.google2fa');

            $user = new User();
            $user->name = $request->name;
            $user->mobile = $request->mobile;
            $user->email = $request->email;
            $user->password = Hash::make($request->password);
            $user->role = USER_ROLE_USER;
            $user->verify_token = str_replace('-', '', Str::uuid()->toString());
            $user->status = USER_STATUS_ACTIVE;
            $user->email_verified_at = Carbon::now()->format("Y-m-d H:i:s");
            $user->google2fa_secret = $google2fa->generateSecretKey();
            $user->email_verification_status = (!empty(getOption('email_verification_status')) && getOption('email_verification_status') == STATUS_ACTIVE) ? STATUS_PENDING : STATUS_ACTIVE;
            $user->save();

            $duration = (int)getOption('trail_duration', 1);

            $defaultPackage = Package::where(['is_trail' => ACTIVE])->first();
            if ($defaultPackage) {
                setUserPackage($user->id, $defaultPackage, $duration);
            }
            syncMissingGateway();
//            setUserGateway($user->id);

            DB::commit();

            $credentials = ['email' => $request->email, 'password' => $request->password];
            if (Auth::attempt($credentials)) {
                return redirect()->back()->with('success', __(CREATED_SUCCESSFULLY));
            } else {
                return redirect()->back()->with('error', __(SOMETHING_WENT_WRONG));
            }
        } catch (Exception $e) {
            DB::rollBack();
            return back()->with('error', $e->getMessage());
        }
    }
}
