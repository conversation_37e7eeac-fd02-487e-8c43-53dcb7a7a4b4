<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ApiKey;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApiKeyController extends Controller
{
    /**
     * Afficher la liste des clés API
     */
    public function index()
    {
        $apiKeys = ApiKey::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.api-keys.index', compact('apiKeys'));
    }

    /**
     * Afficher le formulaire de création
     */
    public function create()
    {
        $users = User::where('role', 'user')->get();
        return view('admin.api-keys.create', compact('users'));
    }

    /**
     * Créer une nouvelle clé API
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'name' => 'required|string|max:255',
            'permissions' => 'nullable|array',
            'permissions.*' => 'string|in:read,write,delete,admin,*',
            'expires_at' => 'nullable|date|after:now'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $apiKey = ApiKey::create([
            'user_id' => $request->user_id,
            'name' => $request->name,
            'permissions' => $request->permissions ?? ['read'],
            'expires_at' => $request->expires_at,
            'is_active' => true
        ]);

        return redirect()->route('admin.api-keys.index')
            ->with('success', 'Clé API créée avec succès');
    }

    /**
     * Afficher une clé API
     */
    public function show($id)
    {
        $apiKey = ApiKey::with('user')->findOrFail($id);
        return view('admin.api-keys.show', compact('apiKey'));
    }

    /**
     * Afficher le formulaire d'édition
     */
    public function edit($id)
    {
        $apiKey = ApiKey::findOrFail($id);
        $users = User::where('role', 'user')->get();
        return view('admin.api-keys.edit', compact('apiKey', 'users'));
    }

    /**
     * Mettre à jour une clé API
     */
    public function update(Request $request, $id)
    {
        $apiKey = ApiKey::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'name' => 'required|string|max:255',
            'permissions' => 'nullable|array',
            'permissions.*' => 'string|in:read,write,delete,admin,*',
            'expires_at' => 'nullable|date|after:now',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $apiKey->update($request->all());

        return redirect()->route('admin.api-keys.index')
            ->with('success', 'Clé API mise à jour avec succès');
    }

    /**
     * Supprimer une clé API
     */
    public function destroy($id)
    {
        $apiKey = ApiKey::findOrFail($id);
        $apiKey->delete();

        return redirect()->route('admin.api-keys.index')
            ->with('success', 'Clé API supprimée avec succès');
    }

    /**
     * Régénérer une clé API
     */
    public function regenerate($id)
    {
        $apiKey = ApiKey::findOrFail($id);
        $apiKey->update(['key' => 'sk_' . \Illuminate\Support\Str::random(48)]);

        return redirect()->route('admin.api-keys.show', $id)
            ->with('success', 'Clé API régénérée avec succès');
    }

    /**
     * Activer/Désactiver une clé API
     */
    public function toggle($id)
    {
        $apiKey = ApiKey::findOrFail($id);
        $apiKey->update(['is_active' => !$apiKey->is_active]);

        $status = $apiKey->is_active ? 'activée' : 'désactivée';

        return redirect()->route('admin.api-keys.index')
            ->with('success', "Clé API {$status} avec succès");
    }
} 