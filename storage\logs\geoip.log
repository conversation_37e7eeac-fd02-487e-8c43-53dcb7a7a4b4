[2025-08-01T16:11:42.046505+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\zaisub\vendor\rappasoft\laravel-authentication-log\src\Listeners\LoginListener.php(38): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Events\Dispatcher.php(441): Rappasoft\LaravelAuthenticationLog\Listeners\LoginListener->handle(Object(Illuminate\Auth\Events\Login)) #4 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Events\Dispatcher.php(249): Illuminate\Events\Dispatcher->Illuminate\Events\{closure}('Illuminate\\Auth...', Array) #5 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Auth\SessionGuard.php(745): Illuminate\Events\Dispatcher->dispatch('Illuminate\\Auth...') #6 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Auth\SessionGuard.php(509): Illuminate\Auth\SessionGuard->fireLoginEvent(Object(App\Models\User), NULL) #7 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Auth\SessionGuard.php(387): Illuminate\Auth\SessionGuard->login(Object(App\Models\User), NULL) #8 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Auth\AuthManager.php(340): Illuminate\Auth\SessionGuard->attempt(Array, NULL) #9 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php(338): Illuminate\Auth\AuthManager->__call('attempt', Array) #10 C:\xampp\htdocs\zaisub\app\Http\Controllers\Auth\LoginController.php(85): Illuminate\Support\Facades\Facade::__callStatic('attempt', Array) #11 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Auth\LoginController->login(Object(App\Http\Requests\LoginRequest)) #12 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('login', Array) #13 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Auth\LoginController), 'login') #14 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController() #15 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(798): Illuminate\Routing\Route->run() #16 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\zaisub\app\Http\Middleware\RedirectIfAuthenticated.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #18 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\RedirectIfAuthenticated->handle(Object(Illuminate\Http\Request), Object(Closure)) #19 C:\xampp\htdocs\zaisub\app\Http\Middleware\VersionUpdate.php(30): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #20 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\VersionUpdate->handle(Object(Illuminate\Http\Request), Object(Closure)) #21 C:\xampp\htdocs\zaisub\app\Http\Middleware\LastUserActivity.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #22 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\LastUserActivity->handle(Object(Illuminate\Http\Request), Object(Closure)) #23 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #24 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #25 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #26 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #27 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #28 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #31 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #32 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #33 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #34 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #35 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #36 C:\xampp\htdocs\zaisub\app\Http\Middleware\InstallMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #37 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\InstallMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure)) #38 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(797): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #40 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #42 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #43 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #44 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #45 C:\xampp\htdocs\zaisub\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(59): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #46 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #47 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #48 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #49 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #53 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #54 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #55 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #56 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #57 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #58 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #59 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #60 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #61 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #62 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #63 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #64 C:\xampp\htdocs\zaisub\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #65 C:\xampp\htdocs\zaisub\server.php(21): require_once('C:\\xampp\\htdocs...') #66 {main} [] []
[2025-08-01T16:11:42.416931+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\zaisub\app\Helpers\Helper.php(1859): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\zaisub\app\Http\Controllers\Auth\LoginController.php(105): addUserActivityLog('Sign In', 1) #4 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Auth\LoginController->login(Object(App\Http\Requests\LoginRequest)) #5 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('login', Array) #6 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Auth\LoginController), 'login') #7 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController() #8 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(798): Illuminate\Routing\Route->run() #9 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request)) #10 C:\xampp\htdocs\zaisub\app\Http\Middleware\RedirectIfAuthenticated.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #11 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\RedirectIfAuthenticated->handle(Object(Illuminate\Http\Request), Object(Closure)) #12 C:\xampp\htdocs\zaisub\app\Http\Middleware\VersionUpdate.php(30): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #13 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\VersionUpdate->handle(Object(Illuminate\Http\Request), Object(Closure)) #14 C:\xampp\htdocs\zaisub\app\Http\Middleware\LastUserActivity.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #15 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\LastUserActivity->handle(Object(Illuminate\Http\Request), Object(Closure)) #16 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #18 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #19 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #20 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #21 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #22 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #23 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #24 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #25 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #26 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #27 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #28 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\zaisub\app\Http\Middleware\InstallMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\InstallMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure)) #31 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #32 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(797): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #33 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #34 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #35 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #36 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #37 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #38 C:\xampp\htdocs\zaisub\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(59): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #40 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #42 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #43 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #44 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #45 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #46 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #47 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #48 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #49 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #53 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #54 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #55 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #56 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #57 C:\xampp\htdocs\zaisub\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #58 C:\xampp\htdocs\zaisub\server.php(21): require_once('C:\\xampp\\htdocs...') #59 {main} [] []
[2025-08-01T16:13:26.889476+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\zaisub\vendor\rappasoft\laravel-authentication-log\src\Listeners\LoginListener.php(38): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Events\Dispatcher.php(441): Rappasoft\LaravelAuthenticationLog\Listeners\LoginListener->handle(Object(Illuminate\Auth\Events\Login)) #4 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Events\Dispatcher.php(249): Illuminate\Events\Dispatcher->Illuminate\Events\{closure}('Illuminate\\Auth...', Array) #5 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Auth\SessionGuard.php(745): Illuminate\Events\Dispatcher->dispatch('Illuminate\\Auth...') #6 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Auth\SessionGuard.php(509): Illuminate\Auth\SessionGuard->fireLoginEvent(Object(App\Models\User), NULL) #7 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Auth\SessionGuard.php(387): Illuminate\Auth\SessionGuard->login(Object(App\Models\User), NULL) #8 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Auth\AuthManager.php(340): Illuminate\Auth\SessionGuard->attempt(Array, NULL) #9 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php(338): Illuminate\Auth\AuthManager->__call('attempt', Array) #10 C:\xampp\htdocs\zaisub\app\Http\Controllers\Auth\LoginController.php(85): Illuminate\Support\Facades\Facade::__callStatic('attempt', Array) #11 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Auth\LoginController->login(Object(App\Http\Requests\LoginRequest)) #12 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('login', Array) #13 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Auth\LoginController), 'login') #14 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController() #15 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(798): Illuminate\Routing\Route->run() #16 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\zaisub\app\Http\Middleware\RedirectIfAuthenticated.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #18 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\RedirectIfAuthenticated->handle(Object(Illuminate\Http\Request), Object(Closure)) #19 C:\xampp\htdocs\zaisub\app\Http\Middleware\VersionUpdate.php(30): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #20 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\VersionUpdate->handle(Object(Illuminate\Http\Request), Object(Closure)) #21 C:\xampp\htdocs\zaisub\app\Http\Middleware\LastUserActivity.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #22 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\LastUserActivity->handle(Object(Illuminate\Http\Request), Object(Closure)) #23 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #24 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #25 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #26 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #27 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #28 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #31 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #32 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #33 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #34 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #35 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #36 C:\xampp\htdocs\zaisub\app\Http\Middleware\InstallMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #37 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\InstallMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure)) #38 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(797): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #40 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #42 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #43 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #44 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #45 C:\xampp\htdocs\zaisub\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(59): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #46 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #47 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #48 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #49 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #53 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #54 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #55 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #56 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #57 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #58 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #59 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #60 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #61 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #62 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #63 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #64 C:\xampp\htdocs\zaisub\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #65 C:\xampp\htdocs\zaisub\server.php(21): require_once('C:\\xampp\\htdocs...') #66 {main} [] []
[2025-08-01T16:13:27.143189+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\zaisub\app\Helpers\Helper.php(1859): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\zaisub\app\Http\Controllers\Auth\LoginController.php(105): addUserActivityLog('Sign In', 2) #4 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Auth\LoginController->login(Object(App\Http\Requests\LoginRequest)) #5 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('login', Array) #6 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Auth\LoginController), 'login') #7 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController() #8 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(798): Illuminate\Routing\Route->run() #9 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request)) #10 C:\xampp\htdocs\zaisub\app\Http\Middleware\RedirectIfAuthenticated.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #11 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\RedirectIfAuthenticated->handle(Object(Illuminate\Http\Request), Object(Closure)) #12 C:\xampp\htdocs\zaisub\app\Http\Middleware\VersionUpdate.php(30): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #13 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\VersionUpdate->handle(Object(Illuminate\Http\Request), Object(Closure)) #14 C:\xampp\htdocs\zaisub\app\Http\Middleware\LastUserActivity.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #15 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\LastUserActivity->handle(Object(Illuminate\Http\Request), Object(Closure)) #16 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #18 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #19 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #20 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #21 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #22 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #23 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #24 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #25 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #26 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #27 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #28 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\zaisub\app\Http\Middleware\InstallMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\InstallMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure)) #31 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #32 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(797): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #33 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #34 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #35 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #36 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #37 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #38 C:\xampp\htdocs\zaisub\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(59): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #40 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #42 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #43 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #44 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #45 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #46 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #47 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #48 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #49 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #53 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #54 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #55 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #56 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #57 C:\xampp\htdocs\zaisub\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #58 C:\xampp\htdocs\zaisub\server.php(21): require_once('C:\\xampp\\htdocs...') #59 {main} [] []
[2025-08-01T16:14:33.876930+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\zaisub\vendor\rappasoft\laravel-authentication-log\src\Listeners\LoginListener.php(38): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Events\Dispatcher.php(441): Rappasoft\LaravelAuthenticationLog\Listeners\LoginListener->handle(Object(Illuminate\Auth\Events\Login)) #4 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Events\Dispatcher.php(249): Illuminate\Events\Dispatcher->Illuminate\Events\{closure}('Illuminate\\Auth...', Array) #5 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Auth\SessionGuard.php(745): Illuminate\Events\Dispatcher->dispatch('Illuminate\\Auth...') #6 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Auth\SessionGuard.php(509): Illuminate\Auth\SessionGuard->fireLoginEvent(Object(App\Models\User), NULL) #7 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Auth\SessionGuard.php(387): Illuminate\Auth\SessionGuard->login(Object(App\Models\User), NULL) #8 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Auth\AuthManager.php(340): Illuminate\Auth\SessionGuard->attempt(Array, NULL) #9 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Support\Facades\Facade.php(338): Illuminate\Auth\AuthManager->__call('attempt', Array) #10 C:\xampp\htdocs\zaisub\app\Http\Controllers\Auth\LoginController.php(85): Illuminate\Support\Facades\Facade::__callStatic('attempt', Array) #11 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Auth\LoginController->login(Object(App\Http\Requests\LoginRequest)) #12 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('login', Array) #13 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Auth\LoginController), 'login') #14 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController() #15 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(798): Illuminate\Routing\Route->run() #16 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\zaisub\app\Http\Middleware\RedirectIfAuthenticated.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #18 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\RedirectIfAuthenticated->handle(Object(Illuminate\Http\Request), Object(Closure)) #19 C:\xampp\htdocs\zaisub\app\Http\Middleware\VersionUpdate.php(30): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #20 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\VersionUpdate->handle(Object(Illuminate\Http\Request), Object(Closure)) #21 C:\xampp\htdocs\zaisub\app\Http\Middleware\LastUserActivity.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #22 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\LastUserActivity->handle(Object(Illuminate\Http\Request), Object(Closure)) #23 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #24 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #25 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #26 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #27 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #28 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #31 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #32 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #33 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #34 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #35 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #36 C:\xampp\htdocs\zaisub\app\Http\Middleware\InstallMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #37 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\InstallMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure)) #38 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(797): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #40 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #42 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #43 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #44 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #45 C:\xampp\htdocs\zaisub\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(59): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #46 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #47 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #48 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #49 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #53 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #54 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #55 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #56 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #57 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #58 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #59 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #60 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #61 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #62 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #63 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #64 C:\xampp\htdocs\zaisub\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #65 C:\xampp\htdocs\zaisub\server.php(21): require_once('C:\\xampp\\htdocs...') #66 {main} [] []
[2025-08-01T16:14:34.142768+00:00] geoip.ERROR: Exception: Request failed (reserved range) in C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\Services\IPApi.php:75 Stack trace: #0 C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\GeoIP.php(149): Torann\GeoIP\Services\IPApi->locate('::1') #1 C:\xampp\htdocs\zaisub\vendor\torann\geoip\src\GeoIP.php(115): Torann\GeoIP\GeoIP->find('::1') #2 C:\xampp\htdocs\zaisub\app\Helpers\Helper.php(1859): Torann\GeoIP\GeoIP->getLocation('::1') #3 C:\xampp\htdocs\zaisub\app\Http\Controllers\Auth\LoginController.php(105): addUserActivityLog('Sign In', 1) #4 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Auth\LoginController->login(Object(App\Http\Requests\LoginRequest)) #5 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('login', Array) #6 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Auth\LoginController), 'login') #7 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController() #8 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(798): Illuminate\Routing\Route->run() #9 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request)) #10 C:\xampp\htdocs\zaisub\app\Http\Middleware\RedirectIfAuthenticated.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #11 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\RedirectIfAuthenticated->handle(Object(Illuminate\Http\Request), Object(Closure)) #12 C:\xampp\htdocs\zaisub\app\Http\Middleware\VersionUpdate.php(30): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #13 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\VersionUpdate->handle(Object(Illuminate\Http\Request), Object(Closure)) #14 C:\xampp\htdocs\zaisub\app\Http\Middleware\LastUserActivity.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #15 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\LastUserActivity->handle(Object(Illuminate\Http\Request), Object(Closure)) #16 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #17 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure)) #18 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #19 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure)) #20 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #21 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #22 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #23 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure)) #24 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure)) #25 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #26 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure)) #27 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #28 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure)) #29 C:\xampp\htdocs\zaisub\app\Http\Middleware\InstallMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #30 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): App\Http\Middleware\InstallMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure)) #31 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #32 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(797): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #33 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(776): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request)) #34 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(740): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route)) #35 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Routing\Router.php(729): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request)) #36 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(190): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request)) #37 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(141): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request)) #38 C:\xampp\htdocs\zaisub\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(59): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #39 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure)) #40 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #41 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #42 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure)) #43 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #44 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure)) #45 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure)) #46 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #47 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure)) #48 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #49 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure)) #50 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #51 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure)) #52 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #53 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(180): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure)) #54 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(116): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request)) #55 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(165): Illuminate\Pipeline\Pipeline->then(Object(Closure)) #56 C:\xampp\htdocs\zaisub\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(134): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request)) #57 C:\xampp\htdocs\zaisub\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request)) #58 C:\xampp\htdocs\zaisub\server.php(21): require_once('C:\\xampp\\htdocs...') #59 {main} [] []
