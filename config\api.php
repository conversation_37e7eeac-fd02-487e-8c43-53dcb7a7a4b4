<?php

return [
    /*
    |--------------------------------------------------------------------------
    | API Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration générale de l'API REST
    |
    */

    'version' => env('API_VERSION', '1.0.0'),
    
    'name' => env('API_NAME', 'SaaS Platform API'),
    
    'description' => env('API_DESCRIPTION', 'API REST pour l\'intégration des plateformes SaaS'),

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configuration de la limitation de débit
    |
    */

    'rate_limiting' => [
        'requests_per_minute' => env('API_RATE_LIMIT_PER_MINUTE', 60),
        'requests_per_hour' => env('API_RATE_LIMIT_PER_HOUR', 1000),
        'requests_per_day' => env('API_RATE_LIMIT_PER_DAY', 10000),
    ],

    /*
    |--------------------------------------------------------------------------
    | API Key Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration des clés API
    |
    */

    'api_keys' => [
        'prefix' => env('API_KEY_PREFIX', 'sk_'),
        'length' => env('API_KEY_LENGTH', 48),
        'default_permissions' => ['read'],
        'max_keys_per_user' => env('API_MAX_KEYS_PER_USER', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Permissions
    |--------------------------------------------------------------------------
    |
    | Permissions disponibles pour les clés API
    |
    */

    'permissions' => [
        'read' => 'Lecture seule',
        'write' => 'Lecture et écriture',
        'delete' => 'Lecture, écriture et suppression',
        'admin' => 'Toutes les permissions administratives',
        '*' => 'Toutes les permissions',
    ],

    /*
    |--------------------------------------------------------------------------
    | Response Format
    |--------------------------------------------------------------------------
    |
    | Format des réponses API
    |
    */

    'response' => [
        'include_timestamp' => true,
        'include_version' => true,
        'pagination' => [
            'default_per_page' => 20,
            'max_per_page' => 100,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Documentation
    |--------------------------------------------------------------------------
    |
    | Configuration de la documentation
    |
    */

    'documentation' => [
        'enabled' => env('API_DOCUMENTATION_ENABLED', true),
        'route' => env('API_DOCUMENTATION_ROUTE', '/api/documentation'),
        'title' => env('API_DOCUMENTATION_TITLE', 'Documentation API'),
        'description' => env('API_DOCUMENTATION_DESCRIPTION', 'Documentation complète de l\'API REST'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging
    |--------------------------------------------------------------------------
    |
    | Configuration du logging des requêtes API
    |
    */

    'logging' => [
        'enabled' => env('API_LOGGING_ENABLED', true),
        'channel' => env('API_LOGGING_CHANNEL', 'api'),
        'log_failed_requests' => env('API_LOG_FAILED_REQUESTS', true),
        'log_successful_requests' => env('API_LOG_SUCCESSFUL_REQUESTS', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security
    |--------------------------------------------------------------------------
    |
    | Configuration de sécurité
    |
    */

    'security' => [
        'require_https' => env('API_REQUIRE_HTTPS', true),
        'allowed_origins' => env('API_ALLOWED_ORIGINS', '*'),
        'cors_enabled' => env('API_CORS_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhooks
    |--------------------------------------------------------------------------
    |
    | Configuration des webhooks
    |
    */

    'webhooks' => [
        'enabled' => env('API_WEBHOOKS_ENABLED', true),
        'timeout' => env('API_WEBHOOK_TIMEOUT', 30),
        'retry_attempts' => env('API_WEBHOOK_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('API_WEBHOOK_RETRY_DELAY', 60),
    ],
]; 